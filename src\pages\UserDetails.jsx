import { useState, useEffect } from "react";
import { useNavigate, useLocation } from 'react-router-dom';
import AnimationBox from '../components/common/AnimationBox';
import Header from '../components/layout/Header';
import { initializeServices } from '../services/index.js';
import '../styles/UserDetails.css';

const UserDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Determine if this is traditional signup (has email) or social signup (no email)
  const isTraditionalSignup = !!location.state?.email;
  const email = location.state?.email || '';

  const [formData, setFormData] = useState({
    university: '',
    branch: '',
    semester: ''
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);

        // Debug logging for signup flow detection
        console.log('🔍 UserDetails Flow Detection:', {
          isTraditionalSignup,
          hasEmail: !!email,
          email: email || 'No email provided',
          locationState: location.state
        });
      } catch (error) {
        console.error('Failed to initialize services:', error);
      }
    };

    initServices();
  }, [isTraditionalSignup, email, location.state]);



  // Sample data for dropdowns with actual UUIDs
  const universities = [
    { value: '', label: 'Select University' },
    { value: 'cad68f6a-cd90-4082-b4a4-4ed9a36ae7df', label: 'University of Pune' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa1', label: 'University of Mumbai' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa2', label: 'University of Delhi' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afa4', label: 'Bangalore University' },
  ];

  const branches = [
    { value: '', label: 'Select Branch' },
    { value: '3c9b98c4-bc4b-4cd4-9ed1-18d7b2e5b76d', label: 'Computer Science Engineering' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb2', label: 'Information Technology' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb3', label: 'Electronics & Communication' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb4', label: 'Mechanical Engineering' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb5', label: 'Civil Engineering' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afb6', label: 'Electrical Engineering' },
  ];

  const semesters = [
    { value: '', label: 'Select Semester' },
    { value: 'f46bcb0a-2bb6-4a55-b1a8-1c1b9db5a490', label: '1st Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc2', label: '2nd Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc3', label: '3rd Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc4', label: '4th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc5', label: '5th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc6', label: '6th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc7', label: '7th Semester' },
    { value: '3fa85f64-5717-4562-b3fc-2c963f66afc8', label: '8th Semester' }
  ];



  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.university) {
      newErrors.university = 'Please select a university';
    }

    if (!formData.branch) {
      newErrors.branch = 'Please select a branch';
    }

    if (!formData.semester) {
      newErrors.semester = 'Please select a semester';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (e) => {
    e.preventDefault();

    if (!validateForm() || !authService) {
      return;
    }

    setIsLoading(true);

    try {
      // Call the signup API with the required payload
      const result = await authService.signup(
        formData.branch,        // branchId
        formData.semester,      // semesterId
        formData.university     // universityId
      );

      if (result.success) {
        console.log('✅ Signup completed successfully');
        alert('User details saved successfully! Your account is now complete.');

        // Different navigation based on signup flow
        if (isTraditionalSignup) {
          // Traditional signup: redirect to login page
          console.log('🔄 Traditional signup completed, redirecting to login page');
          navigate('/login', {
            state: {
              message: 'Account setup completed successfully! Please sign in with your credentials.',
              email: email
            }
          });
        } else {
          // Social signup: redirect to reading screen (user is already authenticated)
          console.log('🔄 Social signup completed, redirecting to reading screen');
          navigate('/reading-screen');
        }
      } else {
        console.error('❌ Signup failed:', result.error);
        alert(`Failed to save user details: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('❌ Signup error:', error);
      alert('An error occurred while saving user details. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="user-details-container">
      <Header />

      <div className="user-details-content">
        <AnimationBox className="user-details-box">
          <h2 className="h2 user-details-title">User Details</h2>

          <form onSubmit={handleSave} className="user-details-form">

            {/* University Dropdown */}
            <div className="form-group">
              <label htmlFor="university" className="body2-bold">University</label>
              <select
                id="university"
                value={formData.university}
                onChange={(e) => handleInputChange('university', e.target.value)}
                className={`dropdown-select ${errors.university ? 'error' : ''}`}
              >
                {universities.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.university && <div className="error-message body4">{errors.university}</div>}
            </div>

            {/* Branch Dropdown */}
            <div className="form-group">
              <label htmlFor="branch" className="body2-bold">Branch</label>
              <select
                id="branch"
                value={formData.branch}
                onChange={(e) => handleInputChange('branch', e.target.value)}
                className={`dropdown-select ${errors.branch ? 'error' : ''}`}
              >
                {branches.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.branch && <div className="error-message body4">{errors.branch}</div>}
            </div>

            {/* Semester Dropdown */}
            <div className="form-group">
              <label htmlFor="semester" className="body2-bold">Semester</label>
              <select
                id="semester"
                value={formData.semester}
                onChange={(e) => handleInputChange('semester', e.target.value)}
                className={`dropdown-select ${errors.semester ? 'error' : ''}`}
              >
                {semesters.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
              {errors.semester && <div className="error-message body4">{errors.semester}</div>}
            </div>



            {/* Save Button */}
            <div className="save-button-container">
              <button
                type="submit"
                className="save-button body2-bold"
                disabled={isLoading}
              >
                {isLoading ? 'Saving...' : 'Complete Signup'}
              </button>
            </div>



          </form>
        </AnimationBox>
      </div>
    </div>
  );
};

export default UserDetails;
