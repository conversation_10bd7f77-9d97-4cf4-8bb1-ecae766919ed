#!/usr/bin/env node

/**
 * Environment Validation Script
 *
 * Validates that Firebase environment configuration is working correctly
 * for both development and production environments.
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
  console.log('\n' + '='.repeat(60));
  console.log(colorize(title, 'bright'));
  console.log('='.repeat(60));
}

function printSuccess(message) {
  console.log(colorize(`✅ ${message}`, 'green'));
}

function printError(message) {
  console.log(colorize(`❌ ${message}`, 'red'));
}

function printWarning(message) {
  console.log(colorize(`⚠️  ${message}`, 'yellow'));
}

function printInfo(message) {
  console.log(colorize(`ℹ️  ${message}`, 'blue'));
}

/**
 * Validate Firebase configuration structure
 */
function validateFirebaseConfig(config, environment) {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
  let isValid = true;

  for (const field of requiredFields) {
    if (!config[field]) {
      printError(`Missing ${field} in ${environment} Firebase config`);
      isValid = false;
    }
  }

  // Validate project ID format
  const expectedSuffix = environment === 'development' ? '-dev' : '';
  const expectedProjectId = `somayya-academy${expectedSuffix}`;

  if (config.projectId !== expectedProjectId) {
    printWarning(`Project ID mismatch in ${environment}: expected ${expectedProjectId}, got ${config.projectId}`);
  }

  return isValid;
}

/**
 * Test environment configuration loading
 */
async function testEnvironmentConfig() {
  printHeader('🌍 TESTING ENVIRONMENT CONFIGURATION');

  try {
    // Test development environment
    process.env.NODE_ENV = 'development';
    const devConfig = await import('../environment/development.js');

    printInfo('Development config loaded');
    console.log(`  API Base URL: ${devConfig.default.api.baseUrl}`);
    console.log(`  Firebase Project: ${devConfig.default.firebase.projectId}`);

    if (validateFirebaseConfig(devConfig.default.firebase, 'development')) {
      printSuccess('Development Firebase config is valid');
    }

    // Test production environment
    const prodConfig = await import('../environment/production.js');

    printInfo('Production config loaded');
    console.log(`  API Base URL: ${prodConfig.default.api.baseUrl}`);
    console.log(`  Firebase Project: ${prodConfig.default.firebase.projectId}`);

    if (validateFirebaseConfig(prodConfig.default.firebase, 'production')) {
      printSuccess('Production Firebase config is valid');
    }

    return true;
  } catch (error) {
    printError(`Environment config test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test build process for different environments
 */
async function testBuildProcess() {
  printHeader('🔨 TESTING BUILD PROCESS');

  const buildCommands = [
    { name: 'Development Build', command: 'npm', args: ['run', 'build:dev'] },
    { name: 'Production Build', command: 'npm', args: ['run', 'build:prod'] }
  ];

  for (const { name, command, args } of buildCommands) {
    printInfo(`Testing ${name}...`);

    try {
      await new Promise((resolve, reject) => {
        const process = spawn(command, args, {
          cwd: rootDir,
          stdio: 'pipe'
        });

        let output = '';
        process.stdout.on('data', (data) => {
          output += data.toString();
        });

        process.stderr.on('data', (data) => {
          output += data.toString();
        });

        process.on('close', (code) => {
          if (code === 0) {
            printSuccess(`${name} completed successfully`);
            resolve();
          } else {
            printError(`${name} failed with code ${code}`);
            console.log(output);
            reject(new Error(`Build failed with code ${code}`));
          }
        });
      });
    } catch (error) {
      printError(`${name} failed: ${error.message}`);
      return false;
    }
  }

  return true;
}

/**
 * Test Firebase CLI configuration
 */
async function testFirebaseConfig() {
  printHeader('🔥 TESTING FIREBASE CLI CONFIGURATION');

  try {
    // Check if Firebase CLI is installed
    await new Promise((resolve, reject) => {
      const process = spawn('firebase', ['--version'], { stdio: 'pipe' });

      process.on('error', (error) => {
        if (error.code === 'ENOENT') {
          printWarning('Firebase CLI not found. Install with: npm install -g firebase-tools');
          resolve(); // Don't fail the test for this
        } else {
          reject(error);
        }
      });

      process.on('close', (code) => {
        if (code === 0) {
          printSuccess('Firebase CLI is installed');
          resolve();
        } else {
          printWarning('Firebase CLI not found. Install with: npm install -g firebase-tools');
          resolve(); // Don't fail the test for this
        }
      });
    });

    // Check Firebase projects configuration
    const fs = await import('fs');
    const firebaserc = join(rootDir, '.firebaserc');

    if (fs.existsSync(firebaserc)) {
      const config = JSON.parse(fs.readFileSync(firebaserc, 'utf8'));

      if (config.projects?.development && config.projects?.production) {
        printSuccess('Firebase project aliases configured correctly');
        console.log(`  Development: ${config.projects.development}`);
        console.log(`  Production: ${config.projects.production}`);
      } else {
        printError('Firebase project aliases not configured properly');
        return false;
      }
    } else {
      printError('.firebaserc file not found');
      return false;
    }

    return true;
  } catch (error) {
    printError(`Firebase config test failed: ${error.message}`);
    return false;
  }
}

/**
 * Main validation function
 */
async function main() {
  console.log(colorize('🚀 Somayya Academy Environment Validation', 'bright'));
  console.log(colorize('Validating Firebase environment configuration...', 'cyan'));

  const tests = [
    { name: 'Environment Configuration', test: testEnvironmentConfig },
    { name: 'Firebase CLI Configuration', test: testFirebaseConfig },
    { name: 'Build Process', test: testBuildProcess }
  ];

  let allPassed = true;

  for (const { name, test } of tests) {
    try {
      const result = await test();
      if (!result) {
        allPassed = false;
      }
    } catch (error) {
      printError(`${name} test failed: ${error.message}`);
      allPassed = false;
    }
  }

  printHeader('📋 VALIDATION SUMMARY');

  if (allPassed) {
    printSuccess('All validation tests passed! 🎉');
    console.log('\n' + colorize('Your Firebase environment configuration is ready to use:', 'green'));
    console.log('  • npm run dev     - Development with dev Firebase project');
    console.log('  • npm run dev:prod - Production mode locally');
    console.log('  • npm run deploy:dev  - Deploy to development');
    console.log('  • npm run deploy:prod - Deploy to production');
  } else {
    printError('Some validation tests failed. Please fix the issues above.');
    process.exit(1);
  }
}

// Run validation
main().catch(console.error);
