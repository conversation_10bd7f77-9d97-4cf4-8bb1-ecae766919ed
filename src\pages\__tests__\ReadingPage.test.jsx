import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import ReadingPage from '../ReadingPage.jsx';

// Mock complex components that might have dependencies
vi.mock('../../components/layout/Header', () => ({
  default: ({ onToggleSidebar, onToggleTools }) => (
    <div data-testid="header">
      <button onClick={onToggleSidebar} data-testid="toggle-sidebar">Toggle Sidebar</button>
      <button onClick={onToggleTools} data-testid="toggle-tools">Toggle Tools</button>
    </div>
  ),
}));

vi.mock('../../components/layout/CourseStructure', () => ({
  default: ({ isVisible, courseData, onTopicSelect }) => (
    <div data-testid="course-structure" style={{ display: isVisible ? 'block' : 'none' }}>
      <button onClick={() => onTopicSelect?.(courseData?.topics?.[0])} data-testid="topic-button">
        {courseData?.topics?.[0]?.title || 'Topic'}
      </button>
    </div>
  ),
}));

vi.mock('../../components/layout/MainContent', () => ({
  default: ({ pdfData, isToolsPanelVisible }) => (
    <div data-testid="main-content" data-tools-visible={isToolsPanelVisible}>
      <div data-testid="pdf-content">{pdfData?.title || 'PDF Content'}</div>
    </div>
  ),
}));

vi.mock('../../components/layout/ToolsPanel', () => ({
  default: ({ isVisible, onClose }) => (
    <div data-testid="tools-panel" style={{ display: isVisible ? 'block' : 'none' }}>
      <button onClick={onClose} data-testid="close-tools">Close</button>
    </div>
  ),
}));

// Mock UI components
vi.mock('../../components/ui/LoadingSpinner', () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

describe('ReadingPage', () => {
  const mockCourseData = {
    id: '1',
    title: 'Test Course',
    topics: [
      {
        id: 'topic1',
        title: 'Topic 1',
        pdfUrl: '/test.pdf',
        content: { heading: 'Test Heading' }
      }
    ]
  };

  const mockPdfData = mockCourseData.topics[0];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Rendering', () => {
    it('should render reading page with all components', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('course-structure')).toBeInTheDocument();
      expect(screen.getByTestId('main-content')).toBeInTheDocument();
      expect(screen.getByTestId('tools-panel')).toBeInTheDocument();
    });

    it('should render with loading state when no data provided', () => {
      renderWithRouter(<ReadingPage />);

      expect(screen.getByTestId('header')).toBeInTheDocument();
    });

    it('should pass correct props to MainContent', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const mainContent = screen.getByTestId('main-content');
      expect(mainContent).toHaveAttribute('data-tools-visible', 'false');
      expect(screen.getByTestId('pdf-content')).toHaveTextContent('Topic 1');
    });
  });

  describe('Sidebar functionality', () => {
    it('should toggle sidebar visibility', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const courseStructure = screen.getByTestId('course-structure');
      const toggleButton = screen.getByTestId('toggle-sidebar');

      // Initially visible
      expect(courseStructure).toHaveStyle({ display: 'block' });

      // Toggle to hide
      fireEvent.click(toggleButton);
      expect(courseStructure).toHaveStyle({ display: 'none' });
    });
  });

  describe('Tools panel functionality', () => {
    it('should toggle tools panel visibility', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const toolsPanel = screen.getByTestId('tools-panel');
      const toggleButton = screen.getByTestId('toggle-tools');

      // Initially hidden
      expect(toolsPanel).toHaveStyle({ display: 'none' });

      // Toggle to show
      fireEvent.click(toggleButton);
      expect(toolsPanel).toHaveStyle({ display: 'block' });
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      expect(screen.getByTestId('toggle-sidebar')).toBeInTheDocument();
      expect(screen.getByTestId('toggle-tools')).toBeInTheDocument();
    });

    it('should support keyboard navigation', () => {
      renderWithRouter(<ReadingPage courseData={mockCourseData} pdfData={mockPdfData} />);

      const toggleButton = screen.getByTestId('toggle-sidebar');
      toggleButton.focus();
      expect(document.activeElement).toBe(toggleButton);
    });
  });
});
