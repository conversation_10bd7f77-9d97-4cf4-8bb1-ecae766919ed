/**
 * API Error Handler - Centralized error processing utilities
 */

/**
 * Error types for categorization
 */
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  AUTHENTICATION: 'AUTHENTICATION_ERROR',
  AUTHORIZATION: 'AUTHORIZATION_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  SERVER: 'SERVER_ERROR',
  RATE_LIMIT: 'RATE_LIMIT_ERROR',
  NOT_FOUND: 'NOT_FOUND_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR',
};

/**
 * User-friendly error messages
 */
export const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: 'Unable to connect to the server. Please check your internet connection.',
  [ERROR_TYPES.AUTHENTICATION]: 'Authentication failed. Please log in again.',
  [ERROR_TYPES.AUTHORIZATION]: 'You do not have permission to perform this action.',
  [ERROR_TYPES.VALIDATION]: 'The information provided is invalid. Please check and try again.',
  [ERROR_TYPES.SERVER]: 'A server error occurred. Please try again later.',
  [ERROR_TYPES.RATE_LIMIT]: 'Too many requests. Please wait a moment before trying again.',
  [ERROR_TYPES.NOT_FOUND]: 'The requested resource was not found.',
  [ERROR_TYPES.UNKNOWN]: 'An unexpected error occurred. Please try again.',
};

/**
 * Categorize error based on status code and error details
 * @param {Error} error - The error object
 * @returns {string} Error type
 */
export const categorizeError = (error) => {
  if (!error.response) {
    return ERROR_TYPES.NETWORK;
  }

  const status = error.response.status;

  switch (status) {
    case 400:
      return ERROR_TYPES.VALIDATION;
    case 401:
      return ERROR_TYPES.AUTHENTICATION;
    case 403:
      return ERROR_TYPES.AUTHORIZATION;
    case 404:
      return ERROR_TYPES.NOT_FOUND;
    case 429:
      return ERROR_TYPES.RATE_LIMIT;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_TYPES.SERVER;
    default:
      return ERROR_TYPES.UNKNOWN;
  }
};

/**
 * Extract error message from error response
 * @param {Error} error - The error object
 * @returns {string} Error message
 */
export const extractErrorMessage = (error) => {
  if (error.response?.data) {
    const data = error.response.data;
    
    // Try different common error message fields
    return data.message || 
           data.error || 
           data.detail || 
           data.errors?.[0]?.message ||
           error.response.statusText ||
           'An error occurred';
  }

  return error.message || 'An unexpected error occurred';
};

/**
 * Create standardized error object
 * @param {Error} error - The original error
 * @returns {Object} Standardized error object
 */
export const createStandardError = (error) => {
  const errorType = categorizeError(error);
  const message = extractErrorMessage(error);
  const userMessage = ERROR_MESSAGES[errorType];

  return {
    type: errorType,
    message,
    userMessage,
    status: error.response?.status,
    statusText: error.response?.statusText,
    timestamp: new Date().toISOString(),
    originalError: error,
  };
};

/**
 * Handle API errors with logging and user-friendly messages
 * @param {Error} error - The error to handle
 * @param {Object} context - Additional context for logging
 * @returns {Object} Processed error object
 */
export const handleApiError = (error, context = {}) => {
  const standardError = createStandardError(error);
  
  // Log error with context
  console.error('API Error:', {
    ...standardError,
    context,
  });

  // You can add additional error handling logic here:
  // - Send to error tracking service (Sentry, LogRocket, etc.)
  // - Show toast notifications
  // - Trigger analytics events
  
  return standardError;
};

/**
 * Check if error is retryable
 * @param {Error} error - The error to check
 * @returns {boolean} Whether the error is retryable
 */
export const isRetryableError = (error) => {
  const retryableStatuses = [408, 429, 500, 502, 503, 504];
  return retryableStatuses.includes(error.response?.status);
};

/**
 * Get retry delay based on error type
 * @param {Error} error - The error object
 * @param {number} attempt - Current retry attempt (0-based)
 * @returns {number} Delay in milliseconds
 */
export const getRetryDelay = (error, attempt = 0) => {
  const baseDelay = 1000; // 1 second
  const maxDelay = 30000; // 30 seconds
  
  // Exponential backoff with jitter
  const delay = Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
  const jitter = Math.random() * 0.1 * delay;
  
  return delay + jitter;
};

/**
 * Service response wrapper for consistent error handling
 * @param {Function} apiCall - The API call function
 * @param {Object} context - Context for error handling
 * @returns {Object} Service response with success/error structure
 */
export const withErrorHandling = async (apiCall, context = {}) => {
  try {
    const data = await apiCall();
    return {
      success: true,
      data,
    };
  } catch (error) {
    const processedError = handleApiError(error, context);
    return {
      success: false,
      error: processedError.userMessage,
      errorDetails: processedError,
    };
  }
};

export default {
  ERROR_TYPES,
  ERROR_MESSAGES,
  categorizeError,
  extractErrorMessage,
  createStandardError,
  handleApiError,
  isRetryableError,
  getRetryDelay,
  withErrorHandling,
};
