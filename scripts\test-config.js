#!/usr/bin/env node

/**
 * Test Configuration Loading
 * 
 * This script tests that the configuration is correctly loading
 * from the environment files and shows the actual values.
 */

// Mock Vite environment for testing
global.import = {
  meta: {
    env: {
      MODE: 'development',
      DEV: true,
      PROD: false
    }
  }
};

async function testConfig() {
  console.log('🧪 Testing Configuration Loading...\n');
  
  try {
    // Test development environment
    console.log('📋 Testing Development Environment:');
    const devConfig = await import('../environment/development.js');
    console.log('  API Base URL:', devConfig.default.api.baseUrl);
    console.log('  Firebase Project:', devConfig.default.firebase.projectId);
    console.log('  Environment:', devConfig.default.env);
    
    console.log('\n📋 Testing Production Environment:');
    const prodConfig = await import('../environment/production.js');
    console.log('  API Base URL:', prodConfig.default.api.baseUrl);
    console.log('  Firebase Project:', prodConfig.default.firebase.projectId);
    console.log('  Environment:', prodConfig.default.env);
    
    console.log('\n✅ Configuration files are loading correctly!');
    console.log('\n🔍 Expected behavior:');
    console.log('  - npm run dev → uses development config');
    console.log('  - npm run dev:prod → uses production config');
    console.log('  - Your updated baseUrl should now be used correctly');
    
  } catch (error) {
    console.error('❌ Configuration test failed:', error);
  }
}

testConfig();
