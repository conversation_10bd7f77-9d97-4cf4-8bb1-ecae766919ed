import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import Header from '../Header.jsx';

describe('Header Component', () => {
  describe('Rendering', () => {
    it('should render header with logo', () => {
      render(<Header />);

      const header = screen.getByRole('img', { name: /logo/i });
      expect(header).toBeInTheDocument();
      expect(header).toHaveAttribute('src', '/icons/logo.png');
      expect(header).toHaveAttribute('alt', 'logo');
    });

    it('should render with header-box class', () => {
      const { container } = render(<Header />);
      
      const headerBox = container.querySelector('.header-box');
      expect(headerBox).toBeInTheDocument();
    });

    it('should render logo container with correct class', () => {
      const { container } = render(<Header />);
      
      const logoContainer = container.querySelector('.header-logo');
      expect(logoContainer).toBeInTheDocument();
    });
  });

  describe('Buttons visibility', () => {
    it('should render buttons by default', () => {
      render(<Header />);

      expect(screen.getByRole('button', { name: /privacy/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /terms/i })).toBeInTheDocument();
    });

    it('should render buttons when showButtons is true', () => {
      render(<Header showButtons={true} />);

      expect(screen.getByRole('button', { name: /privacy/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /terms/i })).toBeInTheDocument();
    });

    it('should not render buttons when showButtons is false', () => {
      render(<Header showButtons={false} />);

      expect(screen.queryByRole('button', { name: /privacy/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /terms/i })).not.toBeInTheDocument();
    });

    it('should render buttons container with correct class when buttons are shown', () => {
      const { container } = render(<Header showButtons={true} />);
      
      const buttonsContainer = container.querySelector('.header-buttons');
      expect(buttonsContainer).toBeInTheDocument();
    });

    it('should not render buttons container when buttons are hidden', () => {
      const { container } = render(<Header showButtons={false} />);
      
      const buttonsContainer = container.querySelector('.header-buttons');
      expect(buttonsContainer).not.toBeInTheDocument();
    });
  });

  describe('Button styling', () => {
    it('should apply correct CSS classes to buttons', () => {
      render(<Header />);

      const privacyButton = screen.getByRole('button', { name: /privacy/i });
      const termsButton = screen.getByRole('button', { name: /terms/i });

      expect(privacyButton).toHaveClass('body3-bold');
      expect(termsButton).toHaveClass('body3-bold');
    });
  });

  describe('Button interactions', () => {
    it('should handle privacy button click', () => {
      render(<Header />);

      const privacyButton = screen.getByRole('button', { name: /privacy/i });
      
      // Button should be clickable (no error thrown)
      expect(() => {
        fireEvent.click(privacyButton);
      }).not.toThrow();
    });

    it('should handle terms button click', () => {
      render(<Header />);

      const termsButton = screen.getByRole('button', { name: /terms/i });
      
      // Button should be clickable (no error thrown)
      expect(() => {
        fireEvent.click(termsButton);
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('should have accessible logo image', () => {
      render(<Header />);

      const logo = screen.getByRole('img', { name: /logo/i });
      expect(logo).toHaveAttribute('alt', 'logo');
    });

    it('should have accessible button text', () => {
      render(<Header />);

      expect(screen.getByRole('button', { name: 'Privacy' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Terms' })).toBeInTheDocument();
    });

    it('should maintain proper heading structure', () => {
      const { container } = render(<Header />);
      
      // Header should be contained in a semantic structure
      const headerBox = container.querySelector('.header-box');
      expect(headerBox).toBeInTheDocument();
    });
  });

  describe('Props handling', () => {
    it('should handle undefined showButtons prop', () => {
      render(<Header showButtons={undefined} />);

      // Should default to showing buttons
      expect(screen.getByRole('button', { name: /privacy/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /terms/i })).toBeInTheDocument();
    });

    it('should handle null showButtons prop', () => {
      render(<Header showButtons={null} />);

      // Should not show buttons when explicitly null
      expect(screen.queryByRole('button', { name: /privacy/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /terms/i })).not.toBeInTheDocument();
    });

    it('should handle string showButtons prop', () => {
      render(<Header showButtons="true" />);

      // Should show buttons for truthy string
      expect(screen.getByRole('button', { name: /privacy/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /terms/i })).toBeInTheDocument();
    });

    it('should handle empty string showButtons prop', () => {
      render(<Header showButtons="" />);

      // Should not show buttons for empty string (falsy)
      expect(screen.queryByRole('button', { name: /privacy/i })).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /terms/i })).not.toBeInTheDocument();
    });
  });

  describe('Component structure', () => {
    it('should maintain correct DOM structure', () => {
      const { container } = render(<Header />);
      
      const headerBox = container.querySelector('.header-box');
      const headerLogo = container.querySelector('.header-logo');
      const headerButtons = container.querySelector('.header-buttons');
      
      expect(headerBox).toBeInTheDocument();
      expect(headerLogo).toBeInTheDocument();
      expect(headerButtons).toBeInTheDocument();
      
      // Logo should be first child
      expect(headerBox.firstElementChild).toBe(headerLogo);
      
      // Buttons should be second child when present
      expect(headerBox.lastElementChild).toBe(headerButtons);
    });

    it('should maintain correct DOM structure without buttons', () => {
      const { container } = render(<Header showButtons={false} />);
      
      const headerBox = container.querySelector('.header-box');
      const headerLogo = container.querySelector('.header-logo');
      const headerButtons = container.querySelector('.header-buttons');
      
      expect(headerBox).toBeInTheDocument();
      expect(headerLogo).toBeInTheDocument();
      expect(headerButtons).not.toBeInTheDocument();
      
      // Logo should be only child
      expect(headerBox.children).toHaveLength(1);
      expect(headerBox.firstElementChild).toBe(headerLogo);
    });
  });

  describe('Image loading', () => {
    it('should handle image load error gracefully', () => {
      render(<Header />);

      const logo = screen.getByRole('img', { name: /logo/i });
      
      // Simulate image load error
      fireEvent.error(logo);
      
      // Component should still be rendered
      expect(logo).toBeInTheDocument();
    });

    it('should have correct image source path', () => {
      render(<Header />);

      const logo = screen.getByRole('img', { name: /logo/i });
      expect(logo.src).toContain('/icons/logo.png');
    });
  });
});
