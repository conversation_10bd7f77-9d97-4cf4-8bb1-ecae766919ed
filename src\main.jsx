import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.jsx';

// Log environment information
console.log(`🚀 Starting Somayya Academy in ${import.meta.env.MODE} mode`);

// Initialize the app with async configuration loading
const initApp = async () => {
  try {
    // Import configuration (this will load from environment files)
    const { default: config } = await import('./config/environment.js');

    console.log('📋 Environment Configuration:', {
      mode: import.meta.env.MODE,
      baseUrl: config.api.baseUrl,
      environment: config.env,
      firebaseProject: config.firebase.projectId,
      isProduction: config.isProduction,
      isDevelopment: config.isDevelopment
    });

    // Initialize the app with loaded configuration
    createRoot(document.getElementById('root')).render(
      <StrictMode>
        <App config={config} />
      </StrictMode>
    );
  } catch (error) {
    console.error('Failed to initialize app:', error);
    // Show error message to user
    document.getElementById('root').innerHTML = `
      <div style="padding: 20px; color: red; font-family: Arial, sans-serif;">
        <h2>Configuration Error</h2>
        <p>Failed to load application configuration: ${error.message}</p>
        <p>Please check the console for more details.</p>
      </div>
    `;
  }
};

// Start the app
initApp();
