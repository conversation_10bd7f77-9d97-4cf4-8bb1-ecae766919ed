import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import CreatePasswordPage from '../CreatePasswordPage.jsx';

// Mock the stores
vi.mock('../../../stores/authStore', () => ({
  useAuthStore: vi.fn(),
}));

// Mock hooks
vi.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: vi.fn(),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('CreatePasswordPage', () => {
  const mockAuthStore = {
    createPassword: vi.fn(),
    isLoading: false,
    error: null,
    user: { email: '<EMAIL>' },
  };

  const mockErrorHandler = {
    error: null,
    handleError: vi.fn(),
    clearError: vi.fn(),
    addError: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();

    const { useAuthStore } = require('../../../stores/authStore');
    const { useErrorHandler } = require('../../../hooks/useErrorHandler');

    useAuthStore.mockReturnValue(mockAuthStore);
    useErrorHandler.mockReturnValue(mockErrorHandler);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Rendering', () => {
    it('should render create password form', () => {
      renderWithRouter(<CreatePasswordPage />);

      expect(screen.getByRole('heading', { name: /create password/i })).toBeInTheDocument();
      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create password/i })).toBeInTheDocument();
    });

    it('should render password requirements', () => {
      renderWithRouter(<CreatePasswordPage />);

      expect(screen.getByText(/at least 8 characters/i)).toBeInTheDocument();
      expect(screen.getByText(/uppercase letter/i)).toBeInTheDocument();
      expect(screen.getByText(/lowercase letter/i)).toBeInTheDocument();
      expect(screen.getByText(/number/i)).toBeInTheDocument();
      expect(screen.getByText(/special character/i)).toBeInTheDocument();
    });

    it('should render password visibility toggles', () => {
      renderWithRouter(<CreatePasswordPage />);

      const toggleButtons = screen.getAllByRole('button', { name: /show password/i });
      expect(toggleButtons).toHaveLength(2); // One for each password field
    });
  });

  describe('Password validation', () => {
    it('should validate password length', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      fireEvent.change(passwordInput, { target: { value: '123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/password must be at least 8 characters/i)).toBeInTheDocument();
      });
    });

    it('should validate uppercase requirement', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'password123!' } });

      await waitFor(() => {
        const uppercaseRequirement = screen.getByText(/uppercase letter/i).closest('li');
        expect(uppercaseRequirement).toHaveClass('invalid');
      });
    });

    it('should validate lowercase requirement', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'PASSWORD123!' } });

      await waitFor(() => {
        const lowercaseRequirement = screen.getByText(/lowercase letter/i).closest('li');
        expect(lowercaseRequirement).toHaveClass('invalid');
      });
    });

    it('should validate number requirement', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'Password!' } });

      await waitFor(() => {
        const numberRequirement = screen.getByText(/number/i).closest('li');
        expect(numberRequirement).toHaveClass('invalid');
      });
    });

    it('should validate special character requirement', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'Password123' } });

      await waitFor(() => {
        const specialCharRequirement = screen.getByText(/special character/i).closest('li');
        expect(specialCharRequirement).toHaveClass('invalid');
      });
    });

    it('should validate password confirmation', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Different123!' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument();
      });
    });

    it('should show valid password when all requirements met', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });

      await waitFor(() => {
        const requirements = screen.getAllByText(/✓/);
        expect(requirements).toHaveLength(5); // All 5 requirements met
      });
    });
  });

  describe('Password visibility', () => {
    it('should toggle password visibility', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const toggleButton = screen.getAllByRole('button', { name: /show password/i })[0];

      expect(passwordInput.type).toBe('password');

      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('text');

      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('password');
    });

    it('should toggle confirm password visibility', () => {
      renderWithRouter(<CreatePasswordPage />);

      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const toggleButton = screen.getAllByRole('button', { name: /show password/i })[1];

      expect(confirmPasswordInput.type).toBe('password');

      fireEvent.click(toggleButton);
      expect(confirmPasswordInput.type).toBe('text');

      fireEvent.click(toggleButton);
      expect(confirmPasswordInput.type).toBe('password');
    });
  });

  describe('Form submission', () => {
    it('should handle successful password creation', async () => {
      mockAuthStore.createPassword.mockResolvedValue({ success: true });

      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAuthStore.createPassword).toHaveBeenCalledWith('Password123!');
        expect(mockNavigate).toHaveBeenCalledWith('/readingScreen');
      });
    });

    it('should handle password creation failure', async () => {
      mockAuthStore.createPassword.mockRejectedValue(new Error('Password creation failed'));

      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockErrorHandler.addError).toHaveBeenCalledWith(
          expect.objectContaining({
            message: expect.stringContaining('password'),
            type: 'error'
          })
        );
      });
    });

    it('should disable submit button during loading', () => {
      mockAuthStore.isLoading = true;

      renderWithRouter(<CreatePasswordPage />);

      const submitButton = screen.getByRole('button', { name: /create password/i });
      expect(submitButton).toBeDisabled();
    });

    it('should disable submit button for invalid passwords', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const submitButton = screen.getByRole('button', { name: /create password/i });

      fireEvent.change(passwordInput, { target: { value: 'weak' } });

      expect(submitButton).toBeDisabled();
    });
  });

  describe('Input interactions', () => {
    it('should update input values', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } });

      expect(passwordInput.value).toBe('Password123!');
      expect(confirmPasswordInput.value).toBe('Password123!');
    });

    it('should handle form submission with Enter key', async () => {
      mockAuthStore.createPassword.mockResolvedValue({ success: true });

      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });
      fireEvent.change(confirmPasswordInput, { target: { value: 'Password123!' } });
      fireEvent.keyDown(confirmPasswordInput, { key: 'Enter', code: 'Enter' });

      await waitFor(() => {
        expect(mockAuthStore.createPassword).toHaveBeenCalledWith('Password123!');
      });
    });
  });

  describe('Error handling', () => {
    it('should display auth store errors', () => {
      mockAuthStore.error = 'Password creation failed';

      renderWithRouter(<CreatePasswordPage />);

      expect(screen.getByText('Password creation failed')).toBeInTheDocument();
    });

    it('should clear errors when form is resubmitted', async () => {
      renderWithRouter(<CreatePasswordPage />);

      const submitButton = screen.getByRole('button', { name: /create password/i });

      fireEvent.click(submitButton);
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockErrorHandler.clearError).toHaveBeenCalled();
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      renderWithRouter(<CreatePasswordPage />);

      expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    });

    it('should have proper ARIA attributes for password requirements', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      expect(passwordInput).toHaveAttribute('aria-describedby');
    });

    it('should support keyboard navigation', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      passwordInput.focus();
      expect(document.activeElement).toBe(passwordInput);

      fireEvent.keyDown(passwordInput, { key: 'Tab' });
      confirmPasswordInput.focus();
      expect(document.activeElement).toBe(confirmPasswordInput);
    });
  });

  describe('Password strength indicator', () => {
    it('should show weak password strength', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'weak' } });

      expect(screen.getByText(/weak/i)).toBeInTheDocument();
    });

    it('should show strong password strength', () => {
      renderWithRouter(<CreatePasswordPage />);

      const passwordInput = screen.getByLabelText(/^password$/i);

      fireEvent.change(passwordInput, { target: { value: 'Password123!' } });

      expect(screen.getByText(/strong/i)).toBeInTheDocument();
    });
  });
});
