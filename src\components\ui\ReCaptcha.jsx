import { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";

/**
 * Reusable ReCAPTCHA component
 * @param {Object} props - Component props
 * @param {string} props.sitekey - ReCAPTCHA site key
 * @param {function} props.onChange - Callback when captcha token changes
 * @param {function} props.onExpired - Callback when captcha expires
 * @param {function} props.onError - Callback when captch<PERSON> encounters an error
 * @param {string} props.theme - Theme ('light' or 'dark')
 * @param {string} props.size - Size ('normal', 'compact', or 'invisible')
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.showError - Whether to show error message
 * @param {string} props.errorMessage - Custom error message
 */
const ReCaptcha = forwardRef(({
  sitekey = "6Le0JIsrAAAAAEh7wqGA6OzWhoTeBxOrirDLyc-u",
  onChange,
  onExpired,
  onError,
  theme = "light",
  size = "normal",
  className = "",
  showError = true,
  errorMessage = "Please complete the CAPTCHA."
}, ref) => {
  const [captchaToken, setCaptchaToken] = useState(null);
  const [error, setError] = useState('');
  const recaptchaRef = useRef(null);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    reset: () => {
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
        setCaptchaToken(null);
        setError('');
      }
    },
    execute: () => {
      if (recaptchaRef.current) {
        recaptchaRef.current.execute();
      }
    },
    getValue: () => captchaToken,
    isValid: () => !!captchaToken
  }));

  const handleChange = (token) => {
    setCaptchaToken(token);
    setError('');
    if (onChange) {
      onChange(token);
    }
  };

  const handleExpired = () => {
    setCaptchaToken(null);
    setError(errorMessage);
    if (onExpired) {
      onExpired();
    }
  };

  const handleError = (error) => {
    setCaptchaToken(null);
    setError('ReCAPTCHA error occurred. Please try again.');
    if (onError) {
      onError(error);
    }
  };

  const validate = () => {
    if (!captchaToken) {
      setError(errorMessage);
      return false;
    }
    setError('');
    return true;
  };

  return (
    <div className={`recaptcha-wrapper ${className}`}>
      <ReCAPTCHA
        ref={recaptchaRef}
        sitekey={sitekey}
        onChange={handleChange}
        onExpired={handleExpired}
        onError={handleError}
        theme={theme}
        size={size}
      />
      {showError && error && <div className="error-message">{error}</div>}
    </div>
  );
});

ReCaptcha.displayName = 'ReCaptcha';

export default ReCaptcha;
