import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useAuthStore } from '../authStore.js';
import { mockUser, mockFirebaseUser } from '../../test/utils.jsx';

// Mock zustand persist middleware
vi.mock('zustand/middleware', () => ({
  persist: (fn) => fn,
}));

describe('authStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useAuthStore.setState({
      currentUser: null,
      isAuthenticated: false,
    });
    localStorage.clear();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useAuthStore.getState();
      expect(state.currentUser).toBeNull();
      expect(state.isAuthenticated).toBe(false);
      expect(typeof state.login).toBe('function');
      expect(typeof state.thirdPartyLogin).toBe('function');
      expect(typeof state.logout).toBe('function');
    });
  });

  describe('login', () => {
    it('should login with valid credentials', () => {
      const { login } = useAuthStore.getState();
      const result = login('<EMAIL>', 'pass1');

      expect(result).toBe(true);

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser).toEqual({
        id: 'user1',
        user: 'user1',
        email: '<EMAIL>',
        password: 'pass1',
      });
    });

    it('should handle case-insensitive email login', () => {
      const { login } = useAuthStore.getState();
      const result = login('<EMAIL>', 'pass1');

      expect(result).toBe(true);

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser.email).toBe('<EMAIL>');
    });

    it('should reject invalid credentials', () => {
      const { login } = useAuthStore.getState();
      const result = login('<EMAIL>', 'wrongpass');

      expect(result).toBe(false);

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.currentUser).toBeNull();
    });

    it('should reject login with wrong password', () => {
      const { login } = useAuthStore.getState();
      const result = login('<EMAIL>', 'wrongpass');

      expect(result).toBe(false);

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.currentUser).toBeNull();
    });

    it('should reject login with wrong email', () => {
      const { login } = useAuthStore.getState();
      const result = login('<EMAIL>', 'pass1');

      expect(result).toBe(false);

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.currentUser).toBeNull();
    });
  });

  describe('thirdPartyLogin', () => {
    it('should login with Firebase user data and call backend API', async () => {
      const { thirdPartyLogin } = useAuthStore.getState();

      // Mock successful API response
      const mockApiResponse = {
        success: true,
        data: {
          statusCode: 200,
          data: {
            userId: 'api-user-id',
            userEmailId: '<EMAIL>',
            token: 'jwt-token-123',
            is_profile_set: false
          }
        }
      };

      // Mock the AuthService socialLogin method
      vi.doMock('../services/authService.js', () => ({
        default: class MockAuthService {
          async socialLogin() {
            return mockApiResponse;
          }
        }
      }));

      const result = await thirdPartyLogin(mockFirebaseUser);

      expect(result).toBe(true);
      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser).toMatchObject({
        id: 'api-user-id',
        email: '<EMAIL>',
        token: 'jwt-token-123',
        is_profile_set: false,
        displayName: mockFirebaseUser.displayName,
        photoURL: mockFirebaseUser.photoURL,
        providerData: mockFirebaseUser.providerData,
      });
    });

    it('should fallback to Firebase-only auth on API failure', async () => {
      const { thirdPartyLogin } = useAuthStore.getState();

      // Mock failed API response
      vi.doMock('../services/authService.js', () => ({
        default: class MockAuthService {
          async socialLogin() {
            throw new Error('API Error');
          }
        }
      }));

      const result = await thirdPartyLogin(mockFirebaseUser);

      expect(result).toBe(true);
      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser).toEqual({
        id: mockFirebaseUser.uid,
        email: mockFirebaseUser.email,
        displayName: mockFirebaseUser.displayName,
        photoURL: mockFirebaseUser.photoURL,
        providerData: mockFirebaseUser.providerData,
      });
    });

    it('should handle Firebase user with minimal data', () => {
      const minimalFirebaseUser = {
        uid: 'minimal-user-id',
      };

      const { thirdPartyLogin } = useAuthStore.getState();
      thirdPartyLogin(minimalFirebaseUser);

      const state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser.id).toBe('minimal-user-id');
      expect(state.currentUser.email).toBeUndefined();
    });
  });

  describe('logout', () => {
    it('should logout user', () => {
      // First login
      const { login, logout } = useAuthStore.getState();
      login('<EMAIL>', 'pass1');

      // Verify logged in
      let state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser).not.toBeNull();

      // Logout
      logout();

      // Verify logged out
      state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.currentUser).toBeNull();
    });

    it('should logout third-party user', () => {
      // First login with third party
      const { thirdPartyLogin, logout } = useAuthStore.getState();
      thirdPartyLogin(mockFirebaseUser);

      // Verify logged in
      let state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.currentUser).not.toBeNull();

      // Logout
      logout();

      // Verify logged out
      state = useAuthStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.currentUser).toBeNull();
    });
  });

  describe('persistence', () => {
    it('should maintain state after logout and login cycle', () => {
      const { login, logout } = useAuthStore.getState();

      // Login
      login('<EMAIL>', 'pass2');
      let state = useAuthStore.getState();
      expect(state.currentUser.email).toBe('<EMAIL>');

      // Logout
      logout();
      state = useAuthStore.getState();
      expect(state.currentUser).toBeNull();

      // Login again
      login('<EMAIL>', 'pass1');
      state = useAuthStore.getState();
      expect(state.currentUser.email).toBe('<EMAIL>');
    });
  });
});
