import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AnimationBox from '../../components/common/AnimationBox';
import Header from '../../components/layout/Header';
import { ReCaptcha } from '../../components/ui';
import { initializeServices } from '../../services';
import { useAuthStore } from '../../stores/authStore';
import { auth, googleProvider, microsoftProvider, facebookProvider } from '../../config/firebase';
import { signInWithPopup } from 'firebase/auth';
import '../../styles/LoginPage.css';

const SignupPage = () => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState('');
  const [captchaToken, setCaptchaToken] = useState(null);
  const [captchaError, setCaptchaError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [authService, setAuthService] = useState(null);

  const navigate = useNavigate();
  const recaptchaRef = useRef(null);

  // Social login handlers
  const handleGoogleSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, googleProvider);
      console.log("🔍 Google sign-up result:", result.user);

      const loginResult = await useAuthStore.getState().thirdPartyLogin(result.user);
      if (loginResult && loginResult.success) {
        // Check if profile is set to determine redirect path
        if (loginResult.is_profile_set) {
          navigate('/reading-screen');
        } else {
          navigate('/userdetails'); // Redirect to user details page if profile not set
        }
      } else {
        console.error('Google sign-up failed - backend authentication unsuccessful');
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Error signing up with Google:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleMicrosoftSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, microsoftProvider);
      console.log("🔍 Microsoft sign-up result:", result.user);

      const loginResult = await useAuthStore.getState().thirdPartyLogin(result.user);
      if (loginResult && loginResult.success) {
        // Check if profile is set to determine redirect path
        if (loginResult.is_profile_set) {
          navigate('/reading-screen');
        } else {
          navigate('/userdetails'); // Redirect to user details page if profile not set
        }
      } else {
        console.error('Microsoft sign-up failed - backend authentication unsuccessful');
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Error signing up with Microsoft:", error);
      // You might want to show an error message to the user here
    }
  };

  const handleFacebookSignIn = async () => {
    try {
      const result = await signInWithPopup(auth, facebookProvider);
      console.log("🔍 Facebook sign-up result:", result.user);

      const loginResult = await useAuthStore.getState().thirdPartyLogin(result.user);
      if (loginResult && loginResult.success) {
        // Check if profile is set to determine redirect path
        if (loginResult.is_profile_set) {
          navigate('/reading-screen');
        } else {
          navigate('/userdetails'); // Redirect to user details page if profile not set
        }
      } else {
        console.error('Facebook sign-up failed - backend authentication unsuccessful');
        // You might want to show an error message to the user here
      }
    } catch (error) {
      console.error("Error signing up with Facebook:", error);
      // You might want to show an error message to the user here
    }
  };

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setCaptchaError('');

    let valid = true;

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      valid = false;
    }

    if (!captchaToken) {
      setCaptchaError('Please complete the CAPTCHA.');
      valid = false;
    }

    if (!authService) {
      setError('Service not available. Please refresh the page.');
      valid = false;
    }

    if (!valid) return;

    setIsLoading(true);

    try {
      // Call the email verification API
      const result = await authService.sendEmailVerification(email, captchaToken);

      if (result.success) {
        // Enhanced debug logging to see exact response structure
        console.log('📧 Email Verification Success - Full Response:', result);
        console.log('📧 Email Verification - Data Object:', result.data);
        console.log('📧 Email Verification - Nested Data:', result.data?.data);
        console.log('📧 Email Verification - Token (data.token):', result.data?.token);
        console.log('📧 Email Verification - Token (data.data.token):', result.data?.data?.token);

        // Try both possible token locations
        const extractedToken = result.data?.data?.token || result.data?.token || '';

        console.log('📧 Email Verification - Extracted Token:', {
          email,
          token: extractedToken,
          tokenLength: extractedToken.length,
          hasToken: !!extractedToken,
          tokenSource: result.data?.data?.token ? 'data.data.token' : (result.data?.token ? 'data.token' : 'not found')
        });

        // Navigate to OTP verification screen on success
        // Pass both email and verification token if available
        navigate('/otp-verification', {
          state: {
            email,
            verificationToken: extractedToken
          }
        });
      } else {
        // Show error message
        setError(result.error || 'Please try again.');

        // Reset reCAPTCHA on error
        if (recaptchaRef.current) {
          recaptchaRef.current.reset();
          setCaptchaToken(null);
        }
      }
    } catch (error) {
      console.error('Email verification error:', error);
      setError('An unexpected error occurred. Please try again.');

      // Reset reCAPTCHA on error
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
        setCaptchaToken(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="login-container">
      <Header />
      <AnimationBox className="login-box">
        <h4 className='h4'>Sign Up</h4>

        <div className='social-login'>
          <button className='social-btn google body2' onClick={handleGoogleSignIn}>Continue with Google<img src='/icons/google.svg' alt='google' /></button>
          <button className='social-btn apple body2'>Continue with Apple<img src='/icons/apple.svg' alt='apple' /></button>
          <button className='social-btn microsoft body2' onClick={handleMicrosoftSignIn}>Continue with Microsoft<img src='/icons/microsoft.svg' alt='microsoft' /></button>
          <button className='social-btn facebook body2' onClick={handleFacebookSignIn}>Continue with Facebook<img src='/icons/facebook.svg' alt='facebook' /></button>
        </div>

        <div className="divider"><span>OR</span></div>

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">Email:</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email address..."
              required
            />
            {error && <div className="error-message">{error}</div>}
          </div>

          <ReCaptcha
            ref={recaptchaRef}
            onChange={(token) => setCaptchaToken(token)}
            showError={false}
          />
          {captchaError && <div className="error-message">{captchaError}</div>}

          <button
            type="submit"
            className="body3-bold login-button"
            disabled={isLoading || !authService}
          >
            {isLoading ? 'Sending...' : 'Continue'}
          </button>
        </form>

        <hr className="hr" />
        <div className="signup">
          <span className='body2'>Already have an account?</span>
          <button
            className='body3-bold'
            onClick={() => navigate('/login')}
          >
            Sign in
          </button>
        </div>
      </AnimationBox>
    </div>
  );
};

export default SignupPage;