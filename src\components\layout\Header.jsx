import { useNavigate } from 'react-router-dom';
import '../../styles/App.css';

const Header = ({ showButtons = true }) => {
    const Navigate =useNavigate();
    return (
        <div className="header-box">
            <div className="header-logo">
                <img src="/icons/logo.png" alt="logo" />
            </div>

            {showButtons && (

                <div className="header-buttons">
                    <button className="body3-bold" onClick ={()=>Navigate('/privacy')}>Privacy</button>
                    <button className="body3-bold" onClick={()=>Navigate('/terms')}>Terms</button>
                </div>
            )}
        </div>
    );
};

export default Header;
