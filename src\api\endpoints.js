/**
 * API Endpoints - Centralized endpoint definitions
 */

const API_ENDPOINTS = {
  // Authentication endpoints
  auth: {
    emailVerification: '/auth/email-verification',
    verifyOTP: '/auth/verify-otp',
    validateOTP: '/auth/validate-otp',
    setPassword: '/auth/set-password',
    resendVerification: '/auth/resend-verification',
    createAccount: '/auth/create-account',
    signup: '/auth/signup',
    signin: '/auth/signin',
    login: '/auth/login',
    logout: '/auth/logout',
    refreshToken: '/auth/refresh',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password',
    changePassword: '/auth/change-password',
  },

  // User endpoints
  user: {
    profile: '/user/profile',
    avatar: '/user/avatar',
    preferences: '/user/preferences',
    activity: '/user/activity',
    account: '/user/account',
  },

  // Content endpoints
  content: {
    courses: '/courses',
    courseById: (courseId) => `/courses/${courseId}`,
    courseContent: (courseId) => `/courses/${courseId}/content`,
    courseProgress: (courseId) => `/courses/${courseId}/progress`,
    pdfContent: (contentId) => `/content/${contentId}/pdf`,
    audioContent: (contentId) => `/content/${contentId}/audio`,
    bookmarks: (contentId) => `/content/${contentId}/bookmarks`,
    highlights: (contentId) => `/content/${contentId}/highlights`,
    annotations: (contentId) => `/content/${contentId}/annotations`,
  },

  // Resource endpoints (for individual resources)
  resources: {
    bookmark: (bookmarkId) => `/bookmarks/${bookmarkId}`,
    highlight: (highlightId) => `/highlights/${highlightId}`,
    annotation: (annotationId) => `/annotations/${annotationId}`,
  },

  // Admin endpoints (future use)
  admin: {
    users: '/admin/users',
    courses: '/admin/courses',
    analytics: '/admin/analytics',
  },

  // System endpoints
  system: {
    health: '/health',
    version: '/version',
    status: '/status',
  },
};

/**
 * Helper function to build endpoint URLs with parameters
 * @param {string} endpoint - Base endpoint
 * @param {Object} params - URL parameters
 * @returns {string} Complete endpoint URL
 */
export const buildEndpoint = (endpoint, params = {}) => {
  let url = endpoint;

  // Replace URL parameters
  Object.keys(params).forEach(key => {
    url = url.replace(`:${key}`, params[key]);
  });

  return url;
};

/**
 * Helper function to get endpoint with query parameters
 * @param {string} endpoint - Base endpoint
 * @param {Object} queryParams - Query parameters
 * @returns {string} Endpoint with query string
 */
export const withQueryParams = (endpoint, queryParams = {}) => {
  const params = new URLSearchParams();

  Object.keys(queryParams).forEach(key => {
    if (queryParams[key] !== null && queryParams[key] !== undefined) {
      params.append(key, queryParams[key]);
    }
  });

  const queryString = params.toString();
  return queryString ? `${endpoint}?${queryString}` : endpoint;
};

/**
 * Validate endpoint exists
 * @param {string} category - Endpoint category
 * @param {string} endpoint - Endpoint name
 * @returns {boolean} Whether endpoint exists
 */
export const validateEndpoint = (category, endpoint) => {
  return API_ENDPOINTS[category] && API_ENDPOINTS[category][endpoint];
};

// Named export for destructuring
export { API_ENDPOINTS };

// Default export
export default API_ENDPOINTS;
