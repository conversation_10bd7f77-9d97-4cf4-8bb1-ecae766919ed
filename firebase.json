{"hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|jsx|ts|tsx|css|html|json|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}]}}