/* UserDetails Page Styles */
.user-details-container {
  min-height: 100vh;
  background-color: var(--bg-color);
  display: flex;
  flex-direction: column;
}

.user-details-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
}

.user-details-box {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.user-details-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--text-color);
}

.user-details-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  color: var(--text-color);
  margin-bottom: 0.25rem;
}

.dropdown-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 10px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: var(--font-family-default);
  font-size: var(--body2-size);
  line-height: var(--body2-line-height);
  cursor: pointer;
  transition: all 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23C3BFB6' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1rem;
  padding-right: 3rem;
}

.dropdown-select:focus {
  outline: none;
  border-color: var(--text-color);
  box-shadow: 0 0 0 2px rgba(17, 17, 17, 0.1);
}

.dropdown-select:hover {
  border-color: var(--secondary-text-color);
}

.dropdown-select.error {
  border-color: #e74c3c;
  box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
}

.dropdown-select option {
  padding: 0.5rem;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.dropdown-select option:hover {
  background-color: var(--secondary-text-color);
  color: var(--bg-color);
}

.dropdown-select option:disabled {
  color: var(--secondary-text-color);
  background-color: var(--bg-color);
}

.error-message {
  color: #e74c3c;
  margin-top: 0.25rem;
  font-size: var(--body4-size);
  line-height: var(--body4-line-height);
}

.save-button-container {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.save-button {
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 10px;
  padding: 0.875rem 2rem;
  font-family: var(--font-family-default);
  font-size: var(--body2-size);
  line-height: var(--body2-line-height);
  font-weight: var(--font-weight-bold);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.save-button:hover {
  background-color: var(--secondary-text-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.save-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.save-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(17, 17, 17, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .user-details-content {
    padding: 1rem;
  }
  
  .user-details-box {
    max-width: 100%;
  }
  
  .user-details-title {
    font-size: var(--h3-size);
    line-height: var(--h3-line-height);
  }
  
  .dropdown-select {
    padding: 0.625rem 0.875rem;
    padding-right: 2.5rem;
    background-size: 0.875rem;
    background-position: right 0.875rem center;
  }
  
  .save-button {
    padding: 0.75rem 1.5rem;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .user-details-content {
    padding: 0.5rem;
  }
  
  .user-details-form {
    gap: 1.25rem;
  }
  
  .dropdown-select {
    padding: 0.5rem 0.75rem;
    padding-right: 2.25rem;
    font-size: var(--body3-size);
    line-height: var(--body3-line-height);
  }
  
  .save-button {
    padding: 0.625rem 1.25rem;
    font-size: var(--body3-size);
    line-height: var(--body3-line-height);
  }
}

/* Animation for form validation */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.dropdown-select.error {
  animation: shake 0.3s ease-in-out;
}

/* Loading state for future enhancements */
.dropdown-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.save-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--secondary-text-color);
}

.save-button:disabled:hover {
  transform: none;
  box-shadow: none;
}