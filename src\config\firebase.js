/**
 * Firebase Configuration
 *
 * Robust, environment-aware Firebase initialization that loads
 * configuration from the same environment files as the rest of the app.
 * This ensures consistency across all configurations.
 *
 * Usage:
 * import { auth, googleProvider, microsoftProvider, facebookProvider } from './config/firebase';
 */

import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { GoogleAuthProvider, OAuthProvider } from "firebase/auth";
import config from './environment.js';

/**
 * Validate Firebase configuration
 */
function validateFirebaseConfig(firebaseConfig, environment) {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];

  for (const field of requiredFields) {
    if (!firebaseConfig[field]) {
      throw new Error(`Missing required Firebase config field: ${field} for environment: ${environment}`);
    }
  }

  // Validate project ID matches environment
  const expectedProjectSuffix = environment === 'development' ? '-dev' : '';
  const expectedProjectId = `somayya-academy${expectedProjectSuffix}`;

  if (firebaseConfig.projectId !== expectedProjectId) {
    console.warn(`Firebase project ID mismatch. Expected: ${expectedProjectId}, Got: ${firebaseConfig.projectId}`);
  }

  return true;
}

// Get Firebase configuration from environment config
const firebaseConfig = config.firebase;
const currentEnvironment = config.env;

if (!firebaseConfig) {
  throw new Error(`No Firebase configuration found in environment config for: ${currentEnvironment}`);
}

// Validate configuration
validateFirebaseConfig(firebaseConfig, currentEnvironment);

// Initialize Firebase with environment-specific configuration
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication
export const auth = getAuth(app);

// Configure Google Auth Provider
export const googleProvider = new GoogleAuthProvider();
googleProvider.addScope('email');
googleProvider.addScope('profile');

// Configure Microsoft Auth Provider
export const microsoftProvider = new OAuthProvider('microsoft.com');

// Configure Facebook Auth Provider
export const facebookProvider = new OAuthProvider('facebook.com');

// Export the Firebase app instance for other services if needed
export default app;

// Export environment info for debugging
export const firebaseEnvironment = {
  environment: currentEnvironment,
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain,
  isDevelopment: currentEnvironment === 'development',
  isProduction: currentEnvironment === 'production'
};

// Log current Firebase configuration (for debugging)
console.log(`🔥 Firebase initialized for ${currentEnvironment} environment:`, {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain,
  environment: currentEnvironment,
  viteMode: import.meta.env.MODE,
  viteDev: import.meta.env.DEV,
  viteProd: import.meta.env.PROD
});
