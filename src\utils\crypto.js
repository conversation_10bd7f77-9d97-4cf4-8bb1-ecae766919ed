/**
 * Hash password using SHA-256 (deterministic - same password always produces same hash)
 * @param {string} password - Plain text password
 * @returns {Promise<string>} SHA-256 hash of the password in hex format
 */
export async function hashPasswordSHA256(password) {
  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);

    // Convert Array<PERSON>uffer to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

    return hashHex;
  } catch (error) {
    console.error('SHA-256 password hashing failed:', error);
    throw new Error('Failed to hash password with SHA-256');
  }
}

/**
 * Encrypt password using AES-GCM with deterministic IV for API transmission
 * This ensures the same password always produces the same encrypted string
 * @param {string} password - Plain text password
 * @param {string} keyStr - Encryption key (default: "somayyaacademy")
 * @returns {Promise<string>} Encrypted password in format "iv:ciphertext"
 */
export async function encryptPassword(password, keyStr = "somayyaacademy") {
  try {
    const enc = new TextEncoder();

    // 1. Hash the key using SHA-256
    const keyBuffer = await crypto.subtle.digest("SHA-256", enc.encode(keyStr));
    const aesKey = keyBuffer.slice(0, 16); // First 16 bytes (128-bit key)

    // 2. Import the key
    const cryptoKey = await crypto.subtle.importKey("raw", aesKey, "AES-GCM", false, ["encrypt"]);

    // 3. Generate deterministic IV from password hash (first 12 bytes)
    // This ensures same password always produces same IV
    const passwordHash = await crypto.subtle.digest("SHA-256", enc.encode(password + keyStr));
    const iv = new Uint8Array(passwordHash.slice(0, 12));

    // 4. Encrypt
    const encrypted = await crypto.subtle.encrypt(
        { name: "AES-GCM", iv },
        cryptoKey,
        enc.encode(password)
    );

    // 5. Encode to base64
    const ivBase64 = btoa(String.fromCharCode(...iv));
    const cipherBase64 = btoa(String.fromCharCode(...new Uint8Array(encrypted)));

    return `${ivBase64}:${cipherBase64}`;
  } catch (error) {
    console.error('Password encryption failed:', error);
    throw new Error('Failed to encrypt password');
  }
}