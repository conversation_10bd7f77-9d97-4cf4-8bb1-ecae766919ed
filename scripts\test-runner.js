#!/usr/bin/env node

/**
 * Comprehensive Test Runner Script
 * 
 * This script provides various testing options for the Somayya Academy application.
 * It can run different types of tests, generate coverage reports, and provide
 * detailed feedback on test results.
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = join(__dirname, '..');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function printHeader(title) {
  console.log('\n' + '='.repeat(60));
  console.log(colorize(title, 'cyan'));
  console.log('='.repeat(60) + '\n');
}

function printSection(title) {
  console.log(colorize(`\n📋 ${title}`, 'yellow'));
  console.log('-'.repeat(40));
}

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      cwd: rootDir,
      stdio: 'inherit',
      shell: true,
      ...options,
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function runAllTests() {
  printHeader('🧪 RUNNING ALL TESTS');
  
  try {
    await runCommand('npm', ['run', 'test:run']);
    console.log(colorize('\n✅ All tests passed!', 'green'));
  } catch (error) {
    console.error(colorize('\n❌ Some tests failed!', 'red'));
    process.exit(1);
  }
}

async function runTestsWithCoverage() {
  printHeader('📊 RUNNING TESTS WITH COVERAGE');
  
  try {
    await runCommand('npm', ['run', 'test:coverage']);
    console.log(colorize('\n✅ Tests completed with coverage report!', 'green'));
    console.log(colorize('📁 Coverage report available in coverage/ directory', 'blue'));
  } catch (error) {
    console.error(colorize('\n❌ Tests failed!', 'red'));
    process.exit(1);
  }
}

async function runWatchMode() {
  printHeader('👀 RUNNING TESTS IN WATCH MODE');
  console.log(colorize('Press Ctrl+C to exit watch mode', 'yellow'));
  
  try {
    await runCommand('npm', ['run', 'test:watch']);
  } catch (error) {
    console.error(colorize('\n❌ Watch mode failed!', 'red'));
    process.exit(1);
  }
}

async function runUIMode() {
  printHeader('🖥️  RUNNING TESTS IN UI MODE');
  console.log(colorize('Opening Vitest UI in your browser...', 'blue'));
  
  try {
    await runCommand('npm', ['run', 'test:ui']);
  } catch (error) {
    console.error(colorize('\n❌ UI mode failed!', 'red'));
    process.exit(1);
  }
}

async function runSpecificTests(pattern) {
  printHeader(`🎯 RUNNING TESTS MATCHING: ${pattern}`);
  
  try {
    await runCommand('npx', ['vitest', 'run', pattern]);
    console.log(colorize('\n✅ Specific tests completed!', 'green'));
  } catch (error) {
    console.error(colorize('\n❌ Specific tests failed!', 'red'));
    process.exit(1);
  }
}

function printUsage() {
  printHeader('🚀 TEST RUNNER USAGE');
  
  console.log(colorize('Available commands:', 'bright'));
  console.log('');
  console.log('  npm run test              - Run all tests once');
  console.log('  npm run test:run          - Run all tests once (alias)');
  console.log('  npm run test:watch        - Run tests in watch mode');
  console.log('  npm run test:ui           - Run tests with UI interface');
  console.log('  npm run test:coverage     - Run tests with coverage report');
  console.log('  npm run test:coverage:ui  - Run tests with coverage and UI');
  console.log('');
  console.log(colorize('Script usage:', 'bright'));
  console.log('');
  console.log('  node scripts/test-runner.js [command] [options]');
  console.log('');
  console.log(colorize('Commands:', 'bright'));
  console.log('  all         - Run all tests once');
  console.log('  coverage    - Run tests with coverage');
  console.log('  watch       - Run tests in watch mode');
  console.log('  ui          - Run tests with UI');
  console.log('  pattern     - Run tests matching pattern (e.g., "stores")');
  console.log('  help        - Show this help message');
  console.log('');
  console.log(colorize('Examples:', 'bright'));
  console.log('  node scripts/test-runner.js all');
  console.log('  node scripts/test-runner.js coverage');
  console.log('  node scripts/test-runner.js pattern stores');
  console.log('  node scripts/test-runner.js pattern "components/ui"');
}

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const options = args.slice(1);

  switch (command) {
    case 'all':
      await runAllTests();
      break;
    
    case 'coverage':
      await runTestsWithCoverage();
      break;
    
    case 'watch':
      await runWatchMode();
      break;
    
    case 'ui':
      await runUIMode();
      break;
    
    case 'pattern':
      if (options.length === 0) {
        console.error(colorize('❌ Pattern command requires a pattern argument', 'red'));
        process.exit(1);
      }
      await runSpecificTests(options[0]);
      break;
    
    case 'help':
    case '--help':
    case '-h':
      printUsage();
      break;
    
    default:
      if (command) {
        console.error(colorize(`❌ Unknown command: ${command}`, 'red'));
      }
      printUsage();
      process.exit(1);
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error(colorize('\n💥 Uncaught Exception:', 'red'));
  console.error(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(colorize('\n💥 Unhandled Rejection at:', 'red'), promise);
  console.error(colorize('Reason:', 'red'), reason);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error(colorize('\n💥 Script failed:', 'red'));
  console.error(error);
  process.exit(1);
});
