import { describe, it, expect, beforeEach } from 'vitest';
import { useColorStore } from '../colorStore.js';

describe('colorStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useColorStore.setState({
      selectedColor: {
        id: 'yellow',
        name: 'Yellow',
        backgroundColor: '#ffeb3b',
        color: '#f57f17'
      },
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useColorStore.getState();
      
      expect(state.selectedColor).toEqual({
        id: 'yellow',
        name: 'Yellow',
        backgroundColor: '#ffeb3b',
        color: '#f57f17'
      });
      expect(typeof state.setSelectedColor).toBe('function');
    });

    it('should have default yellow color selected', () => {
      const { selectedColor } = useColorStore.getState();
      
      expect(selectedColor.id).toBe('yellow');
      expect(selectedColor.name).toBe('Yellow');
      expect(selectedColor.backgroundColor).toBe('#ffeb3b');
      expect(selectedColor.color).toBe('#f57f17');
    });
  });

  describe('setSelectedColor', () => {
    it('should update selected color', () => {
      const { setSelectedColor } = useColorStore.getState();
      const newColor = {
        id: 'red',
        name: 'Red',
        backgroundColor: '#f44336',
        color: '#d32f2f'
      };
      
      setSelectedColor(newColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toEqual(newColor);
    });

    it('should update to blue color', () => {
      const { setSelectedColor } = useColorStore.getState();
      const blueColor = {
        id: 'blue',
        name: 'Blue',
        backgroundColor: '#2196f3',
        color: '#1976d2'
      };
      
      setSelectedColor(blueColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toEqual(blueColor);
      expect(state.selectedColor.id).toBe('blue');
    });

    it('should update to green color', () => {
      const { setSelectedColor } = useColorStore.getState();
      const greenColor = {
        id: 'green',
        name: 'Green',
        backgroundColor: '#4caf50',
        color: '#388e3c'
      };
      
      setSelectedColor(greenColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toEqual(greenColor);
    });

    it('should handle color with additional properties', () => {
      const { setSelectedColor } = useColorStore.getState();
      const colorWithExtras = {
        id: 'purple',
        name: 'Purple',
        backgroundColor: '#9c27b0',
        color: '#7b1fa2',
        opacity: 0.8,
        category: 'highlight'
      };
      
      setSelectedColor(colorWithExtras);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toEqual(colorWithExtras);
      expect(state.selectedColor.opacity).toBe(0.8);
      expect(state.selectedColor.category).toBe('highlight');
    });

    it('should handle minimal color object', () => {
      const { setSelectedColor } = useColorStore.getState();
      const minimalColor = {
        id: 'orange',
        backgroundColor: '#ff9800'
      };
      
      setSelectedColor(minimalColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toEqual(minimalColor);
      expect(state.selectedColor.id).toBe('orange');
      expect(state.selectedColor.backgroundColor).toBe('#ff9800');
    });

    it('should overwrite previous color completely', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      // First color
      const firstColor = {
        id: 'red',
        name: 'Red',
        backgroundColor: '#f44336',
        color: '#d32f2f',
        opacity: 0.5
      };
      
      setSelectedColor(firstColor);
      expect(useColorStore.getState().selectedColor).toEqual(firstColor);
      
      // Second color without opacity
      const secondColor = {
        id: 'blue',
        name: 'Blue',
        backgroundColor: '#2196f3',
        color: '#1976d2'
      };
      
      setSelectedColor(secondColor);
      const finalState = useColorStore.getState();
      expect(finalState.selectedColor).toEqual(secondColor);
      expect(finalState.selectedColor.opacity).toBeUndefined();
    });
  });

  describe('color properties validation', () => {
    it('should handle color with hex values', () => {
      const { setSelectedColor } = useColorStore.getState();
      const hexColor = {
        id: 'custom',
        name: 'Custom',
        backgroundColor: '#123456',
        color: '#abcdef'
      };
      
      setSelectedColor(hexColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor.backgroundColor).toMatch(/^#[0-9a-f]{6}$/i);
      expect(state.selectedColor.color).toMatch(/^#[0-9a-f]{6}$/i);
    });

    it('should handle color with RGB values', () => {
      const { setSelectedColor } = useColorStore.getState();
      const rgbColor = {
        id: 'rgb',
        name: 'RGB Color',
        backgroundColor: 'rgb(255, 0, 0)',
        color: 'rgb(0, 255, 0)'
      };
      
      setSelectedColor(rgbColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor.backgroundColor).toBe('rgb(255, 0, 0)');
      expect(state.selectedColor.color).toBe('rgb(0, 255, 0)');
    });

    it('should handle color with RGBA values', () => {
      const { setSelectedColor } = useColorStore.getState();
      const rgbaColor = {
        id: 'rgba',
        name: 'RGBA Color',
        backgroundColor: 'rgba(255, 0, 0, 0.5)',
        color: 'rgba(0, 255, 0, 0.8)'
      };
      
      setSelectedColor(rgbaColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor.backgroundColor).toBe('rgba(255, 0, 0, 0.5)');
      expect(state.selectedColor.color).toBe('rgba(0, 255, 0, 0.8)');
    });
  });

  describe('edge cases', () => {
    it('should handle null color', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      setSelectedColor(null);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toBeNull();
    });

    it('should handle undefined color', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      setSelectedColor(undefined);
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toBeUndefined();
    });

    it('should handle empty object', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      setSelectedColor({});
      
      const state = useColorStore.getState();
      expect(state.selectedColor).toEqual({});
    });

    it('should handle color with numeric id', () => {
      const { setSelectedColor } = useColorStore.getState();
      const numericIdColor = {
        id: 123,
        name: 'Numeric ID',
        backgroundColor: '#ff0000',
        color: '#000000'
      };
      
      setSelectedColor(numericIdColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor.id).toBe(123);
    });

    it('should handle color with boolean properties', () => {
      const { setSelectedColor } = useColorStore.getState();
      const booleanPropsColor = {
        id: 'boolean',
        name: 'Boolean Props',
        backgroundColor: '#ff0000',
        color: '#000000',
        isDefault: true,
        isActive: false
      };
      
      setSelectedColor(booleanPropsColor);
      
      const state = useColorStore.getState();
      expect(state.selectedColor.isDefault).toBe(true);
      expect(state.selectedColor.isActive).toBe(false);
    });
  });

  describe('state persistence', () => {
    it('should maintain state across multiple operations', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      // Set first color
      const color1 = { id: 'red', backgroundColor: '#f44336' };
      setSelectedColor(color1);
      expect(useColorStore.getState().selectedColor).toEqual(color1);
      
      // Set second color
      const color2 = { id: 'blue', backgroundColor: '#2196f3' };
      setSelectedColor(color2);
      expect(useColorStore.getState().selectedColor).toEqual(color2);
      
      // Set third color
      const color3 = { id: 'green', backgroundColor: '#4caf50' };
      setSelectedColor(color3);
      expect(useColorStore.getState().selectedColor).toEqual(color3);
    });

    it('should not affect other store properties', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      // Verify setSelectedColor function remains unchanged
      const originalSetFunction = useColorStore.getState().setSelectedColor;
      
      setSelectedColor({ id: 'test', backgroundColor: '#000000' });
      
      const newState = useColorStore.getState();
      expect(newState.setSelectedColor).toBe(originalSetFunction);
      expect(typeof newState.setSelectedColor).toBe('function');
    });
  });

  describe('common color scenarios', () => {
    it('should handle typical highlight colors', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      const highlightColors = [
        { id: 'yellow', name: 'Yellow', backgroundColor: '#ffeb3b', color: '#f57f17' },
        { id: 'green', name: 'Green', backgroundColor: '#4caf50', color: '#388e3c' },
        { id: 'blue', name: 'Blue', backgroundColor: '#2196f3', color: '#1976d2' },
        { id: 'red', name: 'Red', backgroundColor: '#f44336', color: '#d32f2f' },
        { id: 'purple', name: 'Purple', backgroundColor: '#9c27b0', color: '#7b1fa2' }
      ];
      
      highlightColors.forEach(color => {
        setSelectedColor(color);
        const state = useColorStore.getState();
        expect(state.selectedColor).toEqual(color);
      });
    });

    it('should handle color switching workflow', () => {
      const { setSelectedColor } = useColorStore.getState();
      
      // Start with default yellow
      expect(useColorStore.getState().selectedColor.id).toBe('yellow');
      
      // Switch to red for important highlights
      setSelectedColor({ id: 'red', backgroundColor: '#f44336', color: '#d32f2f' });
      expect(useColorStore.getState().selectedColor.id).toBe('red');
      
      // Switch to blue for notes
      setSelectedColor({ id: 'blue', backgroundColor: '#2196f3', color: '#1976d2' });
      expect(useColorStore.getState().selectedColor.id).toBe('blue');
      
      // Switch back to yellow
      setSelectedColor({ id: 'yellow', backgroundColor: '#ffeb3b', color: '#f57f17' });
      expect(useColorStore.getState().selectedColor.id).toBe('yellow');
    });
  });
});
