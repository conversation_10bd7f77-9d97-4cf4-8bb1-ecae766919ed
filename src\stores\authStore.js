import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AuthService from '../services/authService.js';

// Initialize auth service
const authService = new AuthService();

// Hardcoded user credentials for demo
const USERS = [
  { id: 'user1', user: 'user1', email: '<EMAIL>', password: 'pass1' },
  { id: 'user2', user: 'user2', email: '<EMAIL>', password: 'pass2' }
];

export const useAuthStore = create(
  persist(
    (set) => ({
      currentUser: null,
      isAuthenticated: false,

      login: async (email, password, recaptchaToken) => {
        try {
          // Use the API service for signin
          const response = await authService.signin(email, password, recaptchaToken);

          console.log('🔍 Login Response Debug:', response);

          if (response && response.success && response.data?.statusCode === 200) {
            // Extract user data from response.data.data
            const apiData = response.data.data;
            const userData = {
              id: apiData?.userId,
              email: apiData?.userEmailId || email,
              token: apiData?.token,
              lastLogoutTime: apiData?.lastLogoutTime,
              ...apiData
            };

            console.log('✅ Login successful, setting user data:', userData);

            set({
              currentUser: userData,
              isAuthenticated: true
            });
            return true;
          }

          console.log('❌ Login failed - invalid response structure');
          return false;
        } catch (error) {
          console.error('Login failed:', error);

          // Fallback to hardcoded users for demo purposes
          const user = USERS.find(
            (u) => u.email.toLowerCase() === email.toLowerCase() && u.password === password
          );
          if (user) {
            console.log('🔄 Using fallback hardcoded user');
            set({ currentUser: user, isAuthenticated: true });
            return true;
          }

          return false;
        }
      },

      thirdPartyLogin: (firebaseUser) => {
        console.log('Firebase user data:', firebaseUser);
        set({
          currentUser: {
            id: firebaseUser.uid,
            email: firebaseUser.email || firebaseUser.providerData?.[0]?.email,
            displayName: firebaseUser.displayName || firebaseUser.providerData?.[0]?.displayName,
            photoURL: firebaseUser.photoURL || firebaseUser.providerData?.[0]?.photoURL,
            providerData: firebaseUser.providerData
          },
          isAuthenticated: true
        });
      },

      logout: () => {
        set({ currentUser: null, isAuthenticated: false });
      }
    }),
    { name: 'auth-storage' }
  )
);
