import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import PublicRoute from '../PublicRoute.jsx';
import { useAuthStore } from '../../stores/authStore.js';

// Mock the auth store
vi.mock('../../stores/authStore.js', () => ({
  useAuthStore: vi.fn(),
}));

// Mock Navigate component
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    Navigate: vi.fn(({ to, replace }) => (
      <div data-testid="navigate" data-to={to} data-replace={replace}>
        Navigate to {to}
      </div>
    )),
  };
});

const TestComponent = () => <div data-testid="public-content">Public Content</div>;

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('PublicRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render children when user is not authenticated', () => {
    useAuthStore.mockReturnValue(false); // isAuthenticated = false

    renderWithRouter(
      <PublicRoute>
        <TestComponent />
      </PublicRoute>
    );

    expect(screen.getByTestId('public-content')).toBeInTheDocument();
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should redirect to reading screen when user is authenticated', () => {
    useAuthStore.mockReturnValue(true); // isAuthenticated = true

    renderWithRouter(
      <PublicRoute>
        <TestComponent />
      </PublicRoute>
    );

    expect(screen.queryByTestId('public-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/reading-screen');
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-replace', 'true');
  });

  it('should handle multiple children when not authenticated', () => {
    useAuthStore.mockReturnValue(false);

    renderWithRouter(
      <PublicRoute>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </PublicRoute>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
  });

  it('should not render children when authenticated', () => {
    useAuthStore.mockReturnValue(true);

    renderWithRouter(
      <PublicRoute>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </PublicRoute>
    );

    expect(screen.queryByTestId('child-1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('child-2')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
  });

  it('should handle null children when not authenticated', () => {
    useAuthStore.mockReturnValue(false);

    renderWithRouter(
      <PublicRoute>
        {null}
      </PublicRoute>
    );

    // Should not throw error and should not redirect
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should handle undefined children when not authenticated', () => {
    useAuthStore.mockReturnValue(false);

    renderWithRouter(
      <PublicRoute>
        {undefined}
      </PublicRoute>
    );

    // Should not throw error and should not redirect
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should re-render when authentication state changes', () => {
    const mockUseAuthStore = vi.fn();
    useAuthStore.mockImplementation(mockUseAuthStore);

    // Start authenticated
    mockUseAuthStore.mockReturnValue(true);

    const { rerender } = renderWithRouter(
      <PublicRoute>
        <TestComponent />
      </PublicRoute>
    );

    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.queryByTestId('public-content')).not.toBeInTheDocument();

    // Change to unauthenticated
    mockUseAuthStore.mockReturnValue(false);

    rerender(
      <BrowserRouter>
        <PublicRoute>
          <TestComponent />
        </PublicRoute>
      </BrowserRouter>
    );

    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    expect(screen.getByTestId('public-content')).toBeInTheDocument();
  });

  it('should work as opposite of PrivateRoute', () => {
    // When not authenticated, PublicRoute shows content, PrivateRoute redirects
    useAuthStore.mockReturnValue(false);

    const { rerender } = renderWithRouter(
      <PublicRoute>
        <TestComponent />
      </PublicRoute>
    );

    expect(screen.getByTestId('public-content')).toBeInTheDocument();

    // When authenticated, PublicRoute redirects, PrivateRoute shows content
    useAuthStore.mockReturnValue(true);

    rerender(
      <BrowserRouter>
        <PublicRoute>
          <TestComponent />
        </PublicRoute>
      </BrowserRouter>
    );

    expect(screen.queryByTestId('public-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
  });
});
