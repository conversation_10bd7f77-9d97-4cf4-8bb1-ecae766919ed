import React, { useEffect, useRef } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import "./styles/App.css";
import { LoginPage, SignupPage, OTPVerificationPage, CreatePasswordPage, ReadingPage, TermsPage,PrivacyPage,UserDetails} from "./pages";
import { ErrorBoundary } from "./components/common";
import { PrivateRoute, PublicRoute } from "./routes";
import { useAuthStore } from "./stores";
import { ToastContainer } from "./components/ui";
import { useErrorHandler } from "./hooks";


// Simple course structure for sidebar
const courseData = {
  id: "1",
  title: "KEPH 102 - Physics Course",
  topics: [
    {
      id: "keph102",
      title: "KEPH 102",
      pdfUrl: "/keph102.pdf",
      audioSources: ["/audio1.mp3", "/audio2.mp3", "/audio3.mp3", "/audio4.mp3"],
      content: {
        heading: "KEPH 102 - Physics Course Material",
        body: [
          {
            id: "content-1",
            type: "pdf",
            pdfUrl: "/keph102.pdf",
          },
        ],
      },
    },
  ],
};

// PDF data for MainContent
const pdfData = courseData.topics[0];

// Main App content component (for testing)
export function AppContent({ config }) {
  const { currentUser, isAuthenticated } = useAuthStore();
  const { error, handleError, clearError, addError } = useErrorHandler();

  // Log the config for debugging
  useEffect(() => {
    console.log('App config:', config);
  }, [config]);

  // Initialize audio context on first user interaction
  useEffect(() => {
    const initializeAudioContext = () => {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      if (AudioContext) {
        const audioContext = new AudioContext();

        // Resume audio context if suspended
        if (audioContext.state === "suspended") {
          audioContext.resume();
        }
      }
    };

    // Add event listener for first user interaction
    const handleFirstInteraction = () => {
      initializeAudioContext();
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };

    document.addEventListener("click", handleFirstInteraction);
    document.addEventListener("touchstart", handleFirstInteraction);

    return () => {
      document.removeEventListener("click", handleFirstInteraction);
      document.removeEventListener("touchstart", handleFirstInteraction);
    };
  }, []);

  return (
    <ErrorBoundary
      message="Something went wrong with the application. Please refresh the page."
      onError={(error, errorInfo) => {
        console.error("App Error Boundary:", error, errorInfo);
        addError({
          id: Date.now(),
          message: "An unexpected error occurred. Please refresh the page.",
          type: "error",
        });
      }}
    >
        <Routes>
          <Route
            path="/login"
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />
          <Route
            path="/privacy"
            element={
              <PublicRoute>
                <PrivacyPage />
              </PublicRoute>
            }
          />
          <Route
          path="/terms"
          element={
            <PublicRoute>
              <TermsPage/>
            </PublicRoute>
          }
          />
          <Route
            path="/signup"
            element={
              <PublicRoute>
                <SignupPage />
              </PublicRoute>
            }
          />
          <Route
            path="/otp-verification"
            element={
              <PublicRoute>
                <OTPVerificationPage />
              </PublicRoute>
            }
          />
          <Route
            path="/set-password"
            element={
              <PublicRoute>
                <CreatePasswordPage />
              </PublicRoute>
            }
          />
          <Route
            path="/user-details"
            element={
              <PublicRoute>
                <UserDetails/>
              </PublicRoute>
            }
          />
          <Route
            path="/reading-screen"
            element={
              <PrivateRoute>
                <ReadingPage courseData={courseData} pdfData={pdfData} />
              </PrivateRoute>
            }
          />
          <Route
            path="/"
            element={<Navigate to={isAuthenticated ? "/reading-screen" : "/login"} replace />}
          />
        </Routes>
        <ToastContainer
          position="bottom-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={false}
          pauseOnFocusLoss
          draggable
          pauseOnHover
          theme="light"
        />
    </ErrorBoundary>
  );
}

// Wrapper component for production use
function AppWithRouter({ config }) {
  return (
    <Router>
      <AppContent config={config} />
    </Router>
  );
}

export default AppWithRouter;
