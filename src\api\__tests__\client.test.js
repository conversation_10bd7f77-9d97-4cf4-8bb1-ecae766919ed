import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import axios from 'axios';
import apiClient from '../client.js';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock environment
vi.mock('../../config/environment.js', () => ({
  default: Promise.resolve({
    api: {
      baseUrl: 'https://api.example.com'
    }
  })
}));

describe('ApiClient', () => {
  let mockAxiosInstance;

  beforeEach(() => {
    // Create mock axios instance
    mockAxiosInstance = {
      post: vi.fn(),
      get: vi.fn(),
      put: vi.fn(),
      patch: vi.fn(),
      delete: vi.fn(),
      defaults: {
        headers: {
          common: {}
        }
      },
      interceptors: {
        request: {
          use: vi.fn()
        },
        response: {
          use: vi.fn()
        }
      }
    };

    // Mock axios.create to return our mock instance
    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    // Reset the client's initialization state
    apiClient.isInitialized = false;
    apiClient.axiosInstance = null;
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with correct config', async () => {
      await apiClient.initialize();

      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://api.example.com',
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    });

    it('should set up interceptors', async () => {
      await apiClient.initialize();

      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });

    it('should not reinitialize if already initialized', async () => {
      await apiClient.initialize();
      await apiClient.initialize();

      expect(mockedAxios.create).toHaveBeenCalledTimes(1);
    });
  });

  describe('HTTP Methods', () => {
    beforeEach(async () => {
      await apiClient.initialize();
    });

    it('should make POST request', async () => {
      const mockResponse = { data: { message: 'Success' } };
      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await apiClient.post('/test', { key: 'value' });

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/test', { key: 'value' }, {});
      expect(result).toEqual({ message: 'Success' });
    });

    it('should make GET request', async () => {
      const mockResponse = { data: { message: 'Success' } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const result = await apiClient.get('/test');

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test', {});
      expect(result).toEqual({ message: 'Success' });
    });
  });

  describe('Authentication', () => {
    beforeEach(async () => {
      await apiClient.initialize();
    });

    it('should set auth token', async () => {
      await apiClient.setAuthToken('test-token');

      expect(mockAxiosInstance.defaults.headers.common['Authorization']).toBe('Bearer test-token');
    });

    it('should remove auth token', async () => {
      await apiClient.setAuthToken('test-token');
      await apiClient.setAuthToken(null);

      expect(mockAxiosInstance.defaults.headers.common['Authorization']).toBeUndefined();
    });
  });
});
