.otp-description {
  text-align: center;
  margin: 1.5rem 0;
}

.otp-description p {
  margin: 0.25rem 0;
  color: var(--secondary-text-color);
}

.email-display {
  color: var(--text-color) !important;
  word-break: break-all;
}

.otp-container {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
}

.otp-input-container {
  display: flex;
  gap: 0.5rem;
  justify-content: center;
}

.otp-input {
  width: 3rem !important;
  height: 3rem !important;
  border: 1px solid var(--border-color) !important;
  border-radius: 10px !important;
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  font-size: 1.25rem !important;
  font-weight: bold !important;
  text-align: center !important;
  outline: none !important;
  transition: all 0.2s ease !important;
}

.otp-input:focus {
  border-color: var(--secondary-text-color) !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.otp-input::-webkit-outer-spin-button,
.otp-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.otp-input[type=number] {
  -moz-appearance: textfield;
}

.otp-separator {
  width: 0.5rem;
}

.resend-section {
  text-align: center;
  margin: 1rem 0;
}

.resend-section p {
  color: var(--secondary-text-color);
  margin: 0;
}

.resend-button {
  background: transparent;
  border: none;
  color: var(--text-color);
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: inherit;
  font-weight: inherit;
}

.resend-button:hover {
  opacity: 0.8;
}

.login-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.login-button:disabled:hover {
  background-color: var(--text-color);
}

@media (max-width: 768px) {
  .otp-input {
    width: 2.5rem !important;
    height: 2.5rem !important;
    font-size: 1rem !important;
  }
  
  .otp-input-container {
    gap: 0.25rem;
  }
}