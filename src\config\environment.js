/**
 * Environment Configuration Loader
 *
 * Dynamically loads the appropriate environment configuration
 * from the environment files based on the current Vite mode.
 *
 * This ensures that all configurations (API, Firebase, etc.)
 * come from the same source files.
 *
 * Usage:
 * import config from './config/environment';
 * console.log(config.api.baseUrl);
 */

/**
 * Get the current environment mode with robust fallback logic
 */
function getCurrentEnvironment() {
  const mode = import.meta.env.MODE;
  const isDev = import.meta.env.DEV;
  const isProd = import.meta.env.PROD;

  // Primary: Use explicit mode
  if (mode === 'development' || mode === 'production') {
    return mode;
  }

  // Fallback: Use Vite's built-in flags
  if (isDev) return 'development';
  if (isProd) return 'production';

  // Final fallback: development (safest for debugging)
  console.warn(`Unknown environment mode: ${mode}. Falling back to development.`);
  return 'development';
}

/**
 * Load configuration from environment files
 */
async function loadEnvironmentConfig() {
  const currentEnvironment = getCurrentEnvironment();

  let config;

  try {
    // Dynamically import the appropriate environment file
    switch (currentEnvironment) {
      case 'development':
        config = await import('../../environment/development.js');
        break;
      case 'production':
        config = await import('../../environment/production.js');
        break;
      default:
        console.warn(`Unknown environment: ${currentEnvironment}. Falling back to development.`);
        config = await import('../../environment/development.js');
    }

    const envConfig = config.default;

    // Add runtime environment info
    envConfig.runtime = {
      viteMode: import.meta.env.MODE,
      viteDev: import.meta.env.DEV,
      viteProd: import.meta.env.PROD,
      nodeEnv: import.meta.env.NODE_ENV,
      detectedEnvironment: currentEnvironment
    };

    // Log environment information
    console.log(`🌍 Environment configuration loaded:`, {
      environment: currentEnvironment,
      apiBaseUrl: envConfig.api.baseUrl,
      firebaseProject: envConfig.firebase.projectId,
      viteMode: import.meta.env.MODE,
      isDev: envConfig.isDevelopment
    });

    return envConfig;

  } catch (error) {
    console.error(`Failed to load environment configuration for ${currentEnvironment}:`, error);
    throw error;
  }
}

// Load and export the configuration
const config = await loadEnvironmentConfig();

export default config;
