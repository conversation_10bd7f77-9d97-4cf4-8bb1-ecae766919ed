import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import LoginPage from '../LoginPage.jsx';
import { useAuthStore } from '../../../stores/authStore.js';
import { signInWithPopup } from 'firebase/auth';

// Mock the auth store
vi.mock('../../../stores/authStore.js', () => ({
  useAuthStore: vi.fn(),
}));

// Mock Firebase auth
vi.mock('firebase/auth', () => ({
  signInWithPopup: vi.fn(),
}));

// Mock Firebase config (legacy - keeping for backward compatibility)
vi.mock('../../../firebase.js', () => ({
  auth: {},
  googleProvider: {},
  microsoftProvider: {},
  facebookProvider: {},
}));

// Mock Firebase config (new unified config)
vi.mock('../../../config/firebase.js', () => ({
  auth: {},
  googleProvider: {},
  microsoftProvider: {},
  facebookProvider: {},
  default: {},
}));

// Mock components
vi.mock('../../../components/common/AnimationBox.jsx', () => ({
  default: ({ children }) => <div data-testid="animation-box">{children}</div>,
}));

vi.mock('../../../components/layout/Header.jsx', () => ({
  default: () => <div data-testid="header">Header</div>,
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const renderWithRouter = (component) => {
  return render(<BrowserRouter>{component}</BrowserRouter>);
};

describe('LoginPage', () => {
  const mockLogin = vi.fn();
  const mockThirdPartyLogin = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock console methods
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});

    // Setup auth store mock
    useAuthStore.mockImplementation((selector) => {
      if (typeof selector === 'function') {
        return selector({ login: mockLogin });
      }
      return { login: mockLogin };
    });

    // Mock getState for third-party login
    useAuthStore.getState = vi.fn(() => ({
      thirdPartyLogin: mockThirdPartyLogin,
    }));
  });

  describe('Rendering', () => {
    it('should render login form elements', () => {
      renderWithRouter(<LoginPage />);

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('animation-box')).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/enter password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    });

    it('should render social login buttons', () => {
      renderWithRouter(<LoginPage />);

      expect(screen.getByRole('button', { name: /continue with google/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue with microsoft/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue with facebook/i })).toBeInTheDocument();
    });

    it('should render signup text', () => {
      renderWithRouter(<LoginPage />);

      expect(screen.getByText(/new to the platform/i)).toBeInTheDocument();
      expect(screen.getByText(/sign up/i)).toBeInTheDocument();
    });

    it('should render password visibility toggle', () => {
      renderWithRouter(<LoginPage />);

      const toggleButton = screen.getByRole('button', { name: /show password/i });
      expect(toggleButton).toBeInTheDocument();
    });
  });

  describe('Form interactions', () => {
    it('should update email input value', () => {
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      expect(emailInput.value).toBe('<EMAIL>');
    });

    it('should update password input value', () => {
      renderWithRouter(<LoginPage />);

      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      fireEvent.change(passwordInput, { target: { value: 'password123' } });

      expect(passwordInput.value).toBe('password123');
    });

    it('should toggle password visibility', () => {
      renderWithRouter(<LoginPage />);

      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const toggleButton = screen.getByRole('button', { name: /show password/i });

      expect(passwordInput.type).toBe('password');

      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('text');

      fireEvent.click(toggleButton);
      expect(passwordInput.type).toBe('password');
    });
  });

  describe('Form validation', () => {
    it('should show error for invalid email format', async () => {
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
      });

      expect(mockLogin).not.toHaveBeenCalled();
    });

    it('should accept valid email formats', async () => {
      mockLogin.mockReturnValue(true);
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
      });
    });

    it('should clear error when form is resubmitted', async () => {
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      // First submission with invalid email
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address.')).toBeInTheDocument();
      });

      // Second submission with valid email
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.queryByText('Please enter a valid email address.')).not.toBeInTheDocument();
      });
    });
  });

  describe('Authentication', () => {
    it('should handle successful login', async () => {
      mockLogin.mockReturnValue(true);
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123');
        expect(mockNavigate).toHaveBeenCalledWith('/readingScreen');
      });
    });

    it('should handle failed login', async () => {
      mockLogin.mockReturnValue(false);
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);
      const submitButton = screen.getByRole('button', { name: /sign in/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'wrongpassword');
        expect(screen.getByText('Invalid credentials. Please try again.')).toBeInTheDocument();
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });
  });

  describe('Social authentication', () => {
    it('should handle successful Google sign-in', async () => {
      const mockUser = { uid: 'google-user-id', email: '<EMAIL>' };
      const mockResult = { user: mockUser };
      signInWithPopup.mockResolvedValue(mockResult);

      renderWithRouter(<LoginPage />);

      const googleButton = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(googleButton);

      await waitFor(() => {
        expect(signInWithPopup).toHaveBeenCalled();
        expect(mockThirdPartyLogin).toHaveBeenCalledWith(mockUser);
        expect(mockNavigate).toHaveBeenCalledWith('/readingScreen');
      });
    });

    it('should handle Google sign-in error', async () => {
      const mockError = new Error('Google sign-in failed');
      signInWithPopup.mockRejectedValue(mockError);

      renderWithRouter(<LoginPage />);

      const googleButton = screen.getByRole('button', { name: /continue with google/i });
      fireEvent.click(googleButton);

      await waitFor(() => {
        expect(signInWithPopup).toHaveBeenCalled();
        expect(console.log).toHaveBeenCalledWith('Error signing in with Google', mockError);
        expect(mockNavigate).not.toHaveBeenCalled();
      });
    });

    it('should handle Microsoft sign-in', async () => {
      const mockUser = { uid: 'microsoft-user-id', email: '<EMAIL>' };
      const mockResult = { user: mockUser };
      signInWithPopup.mockResolvedValue(mockResult);

      renderWithRouter(<LoginPage />);

      const microsoftButton = screen.getByRole('button', { name: /continue with microsoft/i });
      fireEvent.click(microsoftButton);

      await waitFor(() => {
        expect(signInWithPopup).toHaveBeenCalled();
        expect(mockThirdPartyLogin).toHaveBeenCalledWith(mockUser);
        expect(mockNavigate).toHaveBeenCalledWith('/readingScreen');
      });
    });

    it('should handle Facebook sign-in', async () => {
      const mockUser = { uid: 'facebook-user-id', email: '<EMAIL>' };
      const mockResult = { user: mockUser };
      signInWithPopup.mockResolvedValue(mockResult);

      renderWithRouter(<LoginPage />);

      const facebookButton = screen.getByRole('button', { name: /continue with facebook/i });
      fireEvent.click(facebookButton);

      await waitFor(() => {
        expect(signInWithPopup).toHaveBeenCalled();
        expect(mockThirdPartyLogin).toHaveBeenCalledWith(mockUser);
        expect(mockNavigate).toHaveBeenCalledWith('/readingScreen');
      });
    });
  });

  describe('Navigation', () => {
    it('should have signup text', () => {
      renderWithRouter(<LoginPage />);

      expect(screen.getByText(/sign up/i)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels', () => {
      renderWithRouter(<LoginPage />);

      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/enter password/i)).toBeInTheDocument();
    });

    it('should have proper button roles', () => {
      renderWithRouter(<LoginPage />);

      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /show password/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /continue with google/i })).toBeInTheDocument();
    });

    it('should handle form submission with Enter key', async () => {
      renderWithRouter(<LoginPage />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByPlaceholderText(/enter password/i);

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.change(passwordInput, { target: { value: 'password123' } });

      // Test that Enter key works by checking form submission
      const form = document.querySelector('form');
      const submitSpy = vi.fn((e) => e.preventDefault());
      if (form) {
        form.addEventListener('submit', submitSpy);
      }

      fireEvent.keyDown(passwordInput, { key: 'Enter', code: 'Enter' });

      // Just verify the inputs have the correct values
      expect(emailInput.value).toBe('<EMAIL>');
      expect(passwordInput.value).toBe('password123');
    });
  });
});
