import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';

/**
 * Custom render function that includes common providers
 */
export const renderWithRouter = (ui, options = {}) => {
  const Wrapper = ({ children }) => (
    <BrowserRouter>{children}</BrowserRouter>
  );

  return render(ui, { wrapper: Wrapper, ...options });
};

/**
 * Mock user object for testing
 */
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  photoURL: 'https://example.com/photo.jpg',
};

/**
 * Mock Firebase user object
 */
export const mockFirebaseUser = {
  uid: 'firebase-user-id',
  email: '<EMAIL>',
  displayName: 'Firebase User',
  photoURL: 'https://example.com/firebase-photo.jpg',
  providerData: [{
    email: '<EMAIL>',
    displayName: 'Firebase User',
    photoURL: 'https://example.com/firebase-photo.jpg',
  }],
};

/**
 * Mock highlight object for testing
 */
export const mockHighlight = {
  id: 'test-highlight-1',
  content: { text: 'Test highlight text' },
  position: {
    boundingRect: { x1: 100, y1: 100, x2: 200, y2: 120, width: 100, height: 20 },
    rects: [{ x1: 100, y1: 100, x2: 200, y2: 120, width: 100, height: 20 }],
    pageNumber: 1,
  },
  comment: { text: 'Test comment', emoji: '📝' },
  color: { id: 'yellow', backgroundColor: '#ffeb3b', color: '#f57f17' },
};

/**
 * Mock bookmark object for testing
 */
export const mockBookmark = {
  id: 'test-bookmark-1',
  topicId: 'topic-1',
  pageId: 'page-1',
  title: 'Test Bookmark',
  pageNumber: 1,
  createdAt: '2023-01-01T00:00:00.000Z',
};

/**
 * Mock annotation object for testing
 */
export const mockAnnotation = {
  id: 'test-annotation-1',
  topicId: 'topic-1',
  pageId: 'page-1',
  title: 'Test Annotation',
  content: 'Test annotation content',
  pageNumber: 1,
  createdAt: '2023-01-01T00:00:00.000Z',
};

/**
 * Mock topic object for testing
 */
export const mockTopic = {
  id: 'topic-1',
  title: 'Test Topic',
  pdfUrl: '/test.pdf',
  audioSources: ['/audio1.mp3', '/audio2.mp3'],
  content: {
    heading: 'Test Topic Heading',
    body: [
      {
        id: 'content-1',
        type: 'pdf',
        pdfUrl: '/test.pdf',
      },
    ],
  },
};

/**
 * Create a mock store for testing Zustand stores
 */
export const createMockStore = (initialState = {}) => {
  const store = {
    ...initialState,
    subscribe: vi.fn(),
    getState: vi.fn(() => store),
    setState: vi.fn((updater) => {
      if (typeof updater === 'function') {
        Object.assign(store, updater(store));
      } else {
        Object.assign(store, updater);
      }
    }),
  };
  return store;
};

/**
 * Wait for async operations to complete
 */
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

/**
 * Mock audio element for testing
 */
export const mockAudioElement = {
  play: vi.fn().mockResolvedValue(undefined),
  pause: vi.fn(),
  load: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  currentTime: 0,
  duration: 100,
  paused: true,
  ended: false,
  volume: 1,
  muted: false,
  src: '',
};

/**
 * Mock HTMLAudioElement constructor
 */
export const mockAudioConstructor = vi.fn(() => mockAudioElement);

// Set up global Audio mock
beforeEach(() => {
  global.Audio = mockAudioConstructor;
});
