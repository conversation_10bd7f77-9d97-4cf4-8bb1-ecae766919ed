/**
 * Services - Main service layer exports
 */
import AuthService from './authService.js';
import { apiClient } from '../api/index.js';

// Initialize all services
const initializeServices = async () => {
  // Ensure API client is initialized
  await apiClient.initialize();

  return {
    // API client (direct access if needed)
    apiClient,

    // Service layer (business logic)
    authService: new AuthService(),

    // You can add more service layers here as needed
    // userService: new UserService(),
    // contentService: new ContentService(),
  };
};

// Export service classes for testing
export { AuthService, initializeServices };
