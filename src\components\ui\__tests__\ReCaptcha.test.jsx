import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ReCaptcha from '../ReCaptcha.jsx';

// Mock the react-google-recaptcha component
vi.mock('react-google-recaptcha', () => ({
  default: vi.fn(({ onChange, onExpired, onError, sitekey, theme, size }) => (
    <div data-testid="mock-recaptcha">
      <button
        data-testid="recaptcha-trigger"
        onClick={() => onChange && onChange('mock-token')}
      >
        Complete CAPTCHA
      </button>
      <button
        data-testid="recaptcha-expire"
        onClick={() => onExpired && onExpired()}
      >
        Expire CAPTCHA
      </button>
      <button
        data-testid="recaptcha-error"
        onClick={() => onError && onError('mock-error')}
      >
        Trigger Error
      </button>
      <div data-testid="recaptcha-props">
        {JSON.stringify({ sitekey, theme, size })}
      </div>
    </div>
  ))
}));

describe('ReCaptcha', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Rendering', () => {
    it('should render ReCaptcha component', () => {
      render(<ReCaptcha />);
      
      expect(screen.getByTestId('mock-recaptcha')).toBeInTheDocument();
      expect(screen.getByTestId('recaptcha-trigger')).toBeInTheDocument();
    });

    it('should render with default props', () => {
      render(<ReCaptcha />);
      
      const propsElement = screen.getByTestId('recaptcha-props');
      const props = JSON.parse(propsElement.textContent);
      
      expect(props.sitekey).toBe('6Le0JIsrAAAAAEh7wqGA6OzWhoTeBxOrirDLyc-u');
      expect(props.theme).toBe('light');
      expect(props.size).toBe('normal');
    });

    it('should render with custom props', () => {
      render(
        <ReCaptcha
          sitekey="custom-sitekey"
          theme="dark"
          size="compact"
        />
      );
      
      const propsElement = screen.getByTestId('recaptcha-props');
      const props = JSON.parse(propsElement.textContent);
      
      expect(props.sitekey).toBe('custom-sitekey');
      expect(props.theme).toBe('dark');
      expect(props.size).toBe('compact');
    });
  });

  describe('Token handling', () => {
    it('should call onChange when captcha is completed', () => {
      const mockOnChange = vi.fn();
      render(<ReCaptcha onChange={mockOnChange} />);
      
      const triggerButton = screen.getByTestId('recaptcha-trigger');
      fireEvent.click(triggerButton);
      
      expect(mockOnChange).toHaveBeenCalledWith('mock-token');
    });

    it('should call onExpired when captcha expires', () => {
      const mockOnExpired = vi.fn();
      render(<ReCaptcha onExpired={mockOnExpired} />);
      
      const expireButton = screen.getByTestId('recaptcha-expire');
      fireEvent.click(expireButton);
      
      expect(mockOnExpired).toHaveBeenCalled();
    });

    it('should call onError when captcha encounters error', () => {
      const mockOnError = vi.fn();
      render(<ReCaptcha onError={mockOnError} />);
      
      const errorButton = screen.getByTestId('recaptcha-error');
      fireEvent.click(errorButton);
      
      expect(mockOnError).toHaveBeenCalledWith('mock-error');
    });
  });

  describe('Error display', () => {
    it('should show error message when captcha expires', () => {
      render(<ReCaptcha />);
      
      const expireButton = screen.getByTestId('recaptcha-expire');
      fireEvent.click(expireButton);
      
      expect(screen.getByText('Please complete the CAPTCHA.')).toBeInTheDocument();
    });

    it('should show custom error message', () => {
      render(<ReCaptcha errorMessage="Custom error message" />);
      
      const expireButton = screen.getByTestId('recaptcha-expire');
      fireEvent.click(expireButton);
      
      expect(screen.getByText('Custom error message')).toBeInTheDocument();
    });

    it('should not show error when showError is false', () => {
      render(<ReCaptcha showError={false} />);
      
      const expireButton = screen.getByTestId('recaptcha-expire');
      fireEvent.click(expireButton);
      
      expect(screen.queryByText('Please complete the CAPTCHA.')).not.toBeInTheDocument();
    });

    it('should show error message when captcha encounters error', () => {
      render(<ReCaptcha />);
      
      const errorButton = screen.getByTestId('recaptcha-error');
      fireEvent.click(errorButton);
      
      expect(screen.getByText('ReCAPTCHA error occurred. Please try again.')).toBeInTheDocument();
    });
  });

  describe('CSS classes', () => {
    it('should apply default wrapper class', () => {
      const { container } = render(<ReCaptcha />);
      
      expect(container.querySelector('.recaptcha-wrapper')).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      const { container } = render(<ReCaptcha className="custom-class" />);
      
      expect(container.querySelector('.recaptcha-wrapper.custom-class')).toBeInTheDocument();
    });
  });
});
