import React from 'react';
import '../../styles/LoadingSpinner.css';

/**
 * Loading spinner component with different sizes and variants
 */
const LoadingSpinner = ({
  size = 'medium',
  variant = 'default',
  message = '',
  overlay = false,
  className = ''
}) => {
  const spinnerClasses = [
    'loading-spinner',
    `loading-spinner--${size}`,
    `loading-spinner--${variant}`,
    className
  ].filter(Boolean).join(' ');

  const content = (
    <div className={spinnerClasses} role="status" aria-label={message || 'Loading'}>
      <div className="loading-spinner__circle">
        <div className="loading-spinner__inner"></div>
      </div>
      {message && (
        <div className="loading-spinner__message">
          {message}
        </div>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="loading-spinner-overlay">
        {content}
      </div>
    );
  }

  return content;
};

/**
 * Inline loading component for buttons and small spaces
 */
export const InlineLoader = ({ size = 'small', className = '' }) => {
  return (
    <div className={`inline-loader inline-loader--${size} ${className}`}>
      <div className="inline-loader__dot"></div>
      <div className="inline-loader__dot"></div>
      <div className="inline-loader__dot"></div>
    </div>
  );
};

/**
 * Skeleton loader for content placeholders
 */
export const SkeletonLoader = ({
  width = '100%',
  height = '20px',
  borderRadius = '4px',
  className = ''
}) => {
  // Convert numeric values to px strings
  const normalizeSize = (size) => {
    if (typeof size === 'number') {
      return `${size}px`;
    }
    return size;
  };

  return (
    <div
      className={`skeleton-loader ${className}`}
      style={{
        width: normalizeSize(width),
        height: normalizeSize(height),
        borderRadius: normalizeSize(borderRadius)
      }}
    />
  );
};

/**
 * Text skeleton loader
 */
export const TextSkeleton = ({ lines = 3, className = '' }) => {
  return (
    <div className={`text-skeleton ${className}`}>
      {Array.from({ length: lines }, (_, index) => (
        <SkeletonLoader
          key={index}
          height="16px"
          width={index === lines - 1 ? '60%' : '100%'}
          className="text-skeleton__line"
        />
      ))}
    </div>
  );
};

/**
 * Audio player skeleton loader
 */
export const AudioPlayerSkeleton = ({ className = '' }) => {
  return (
    <div className={`audio-player-skeleton ${className}`}>
      <div className="audio-player-skeleton__header">
        <SkeletonLoader width="200px" height="18px" />
      </div>
      <div className="audio-player-skeleton__controls">
        <SkeletonLoader width="40px" height="40px" borderRadius="50%" />
        <SkeletonLoader width="100%" height="6px" borderRadius="3px" />
        <SkeletonLoader width="60px" height="16px" />
      </div>
    </div>
  );
};

export default LoadingSpinner;
