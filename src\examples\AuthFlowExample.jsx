/**
 * Authentication Flow Example
 * 
 * This example demonstrates how to use the new OTP validation
 * and password setting methods in a React component.
 */

import { useState, useEffect } from 'react';
import { initializeServices } from '../services/index.js';

const AuthFlowExample = () => {
  const [authService, setAuthService] = useState(null);
  const [currentStep, setCurrentStep] = useState('email'); // email, otp, password, complete
  const [formData, setFormData] = useState({
    email: '',
    otp: '',
    password: '',
    confirmPassword: '',
    recaptchaToken: '',
    verificationToken: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Initialize services
  useEffect(() => {
    const initServices = async () => {
      try {
        const services = await initializeServices();
        setAuthService(services.authService);
      } catch (error) {
        console.error('Failed to initialize services:', error);
        setError('Failed to initialize application. Please refresh the page.');
      }
    };

    initServices();
  }, []);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError('');
    setSuccess('');
  };

  // Step 1: Send email verification
  const handleSendVerification = async (e) => {
    e.preventDefault();
    if (!authService) return;

    setLoading(true);
    setError('');

    try {
      const result = await authService.sendEmailVerification(
        formData.email,
        formData.recaptchaToken
      );

      if (result.success) {
        setSuccess('Verification email sent! Please check your inbox.');
        // Store verification token if provided
        if (result.data?.token) {
          setFormData(prev => ({ ...prev, verificationToken: result.data.token }));
        }
        setCurrentStep('otp');
      } else {
        setError(result.error || 'Failed to send verification email');
      }
    } catch (error) {
      setError('An unexpected error occurred');
      console.error('Email verification error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Step 2: Validate OTP
  const handleValidateOTP = async (e) => {
    e.preventDefault();
    if (!authService) return;

    setLoading(true);
    setError('');

    try {
      const result = await authService.validateOTP(
        formData.email,
        formData.otp,
        formData.verificationToken,
        'sign_up'
      );

      if (result.success) {
        setSuccess('OTP validated successfully!');
        // Update verification token if new one is provided
        if (result.data?.token) {
          setFormData(prev => ({ ...prev, verificationToken: result.data.token }));
        }
        setCurrentStep('password');
      } else {
        setError(result.error || 'Invalid OTP code');
      }
    } catch (error) {
      setError('An unexpected error occurred');
      console.error('OTP validation error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Step 3: Set password
  const handleSetPassword = async (e) => {
    e.preventDefault();
    if (!authService) return;

    setLoading(true);
    setError('');

    try {
      const result = await authService.setPassword(
        formData.email,
        formData.password,
        formData.confirmPassword
      );

      if (result.success) {
        setSuccess('Account created successfully!');
        setCurrentStep('complete');
      } else {
        setError(result.error || 'Failed to set password');
      }
    } catch (error) {
      setError('An unexpected error occurred');
      console.error('Set password error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Render different steps
  const renderStep = () => {
    switch (currentStep) {
      case 'email':
        return (
          <form onSubmit={handleSendVerification}>
            <h2>Step 1: Email Verification</h2>
            <div>
              <label htmlFor="email">Email Address:</label>
              <input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="recaptcha">ReCAPTCHA Token:</label>
              <input
                id="recaptcha"
                type="text"
                value={formData.recaptchaToken}
                onChange={(e) => handleInputChange('recaptchaToken', e.target.value)}
                placeholder="ReCAPTCHA token"
                required
                disabled={loading}
              />
            </div>
            <button type="submit" disabled={loading}>
              {loading ? 'Sending...' : 'Send Verification Email'}
            </button>
          </form>
        );

      case 'otp':
        return (
          <form onSubmit={handleValidateOTP}>
            <h2>Step 2: OTP Verification</h2>
            <p>Please enter the OTP code sent to {formData.email}</p>
            <div>
              <label htmlFor="otp">OTP Code:</label>
              <input
                id="otp"
                type="text"
                value={formData.otp}
                onChange={(e) => handleInputChange('otp', e.target.value)}
                placeholder="Enter 6-digit code"
                maxLength="6"
                required
                disabled={loading}
              />
            </div>
            <button type="submit" disabled={loading}>
              {loading ? 'Validating...' : 'Validate OTP'}
            </button>
            <button type="button" onClick={() => setCurrentStep('email')}>
              Back to Email
            </button>
          </form>
        );

      case 'password':
        return (
          <form onSubmit={handleSetPassword}>
            <h2>Step 3: Set Password</h2>
            <div>
              <label htmlFor="password">Password:</label>
              <input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <div>
              <label htmlFor="confirmPassword">Confirm Password:</label>
              <input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                required
                disabled={loading}
              />
            </div>
            <button type="submit" disabled={loading}>
              {loading ? 'Setting Password...' : 'Set Password'}
            </button>
            <button type="button" onClick={() => setCurrentStep('otp')}>
              Back to OTP
            </button>
          </form>
        );

      case 'complete':
        return (
          <div>
            <h2>Registration Complete!</h2>
            <p>Your account has been created successfully.</p>
            <p>You can now log in with your email and password.</p>
            <button onClick={() => {
              setCurrentStep('email');
              setFormData({
                email: '',
                otp: '',
                password: '',
                confirmPassword: '',
                recaptchaToken: '',
                verificationToken: ''
              });
            }}>
              Start Over
            </button>
          </div>
        );

      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <div style={{ maxWidth: '400px', margin: '0 auto', padding: '20px' }}>
      <h1>Authentication Flow Example</h1>
      
      {error && (
        <div style={{ color: 'red', marginBottom: '10px' }}>
          {error}
        </div>
      )}
      
      {success && (
        <div style={{ color: 'green', marginBottom: '10px' }}>
          {success}
        </div>
      )}
      
      {renderStep()}
      
      <div style={{ marginTop: '20px', fontSize: '12px', color: '#666' }}>
        <p><strong>API Endpoints Used:</strong></p>
        <ul>
          <li>POST /auth/email-verification</li>
          <li>POST /auth/validate-otp</li>
          <li>POST /auth/set-password</li>
        </ul>
      </div>
    </div>
  );
};

export default AuthFlowExample;
