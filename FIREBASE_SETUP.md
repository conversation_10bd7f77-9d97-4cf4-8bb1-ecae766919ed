# Firebase Environment Configuration

This document explains the **optimized and robust** Firebase environment configuration for development and production environments.

## 🏗️ Project Structure

### Firebase Projects
- **Development**: `somayya-academy-dev`
- **Production**: `somayya-academy`

### Configuration Architecture
- `src/config/firebase.js` - **Optimized** unified Firebase configuration with validation
- `src/config/environment.js` - **Synchronous** environment configuration loader
- `environment/development.js` - Legacy development config (kept for compatibility)
- `environment/production.js` - Legacy production config (kept for compatibility)

## ✨ Key Optimizations

### 🚀 **Synchronous Configuration Loading**
- No more async imports or top-level await
- Faster app initialization
- Better error handling and validation

### 🛡️ **Robust Environment Detection**
- Multiple fallback mechanisms for environment detection
- Validates Firebase configurations at startup
- Clear error messages for misconfigurations

### 📦 **Optimized Bundle Size**
- Configurations are bundled at build time
- No dynamic imports at runtime
- Tree-shaking friendly

## 🚀 Development Workflow

### Running Locally

```bash
# Development mode (uses somayya-academy-dev Firebase project)
npm run dev          # ✅ GUARANTEED to use dev Firebase project
npm run start:dev    # Same as above

# Production mode locally (uses somayya-academy Firebase project)
npm run dev:prod     # ✅ GUARANTEED to use prod Firebase project
npm run start:prod   # Same as above
```

### **Environment Guarantee**
When you run `npm run dev`, the system **automatically**:
1. ✅ Sets Vite mode to `development`
2. ✅ Loads development Firebase configuration
3. ✅ Connects to `somayya-academy-dev` project
4. ✅ Uses development API endpoints
5. ✅ Logs confirmation in console

### Building for Different Environments

```bash
# Build for development (uses dev Firebase project)
npm run build:dev

# Build for production (uses prod Firebase project)
npm run build:prod

# Default build (production)
npm run build
```

### **Validation & Testing**

```bash
# Validate entire environment setup
npm run validate

# Test that everything works correctly
npm run test:run

# Preview builds locally
npm run preview:dev   # Preview development build
npm run preview:prod  # Preview production build
```

## 🌐 Deployment

### Prerequisites
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Ensure you have access to both Firebase projects

### Deploy to Development
```bash
npm run deploy:dev
```
This will:
1. Build the app in development mode
2. Switch to the development Firebase project
3. Deploy to Firebase Hosting

### Deploy to Production
```bash
npm run deploy:prod
```
This will:
1. Build the app in production mode
2. Switch to the production Firebase project
3. Deploy to Firebase Hosting

### Manual Firebase Project Switching
```bash
# Switch to development project
npm run firebase:dev
# or
firebase use development

# Switch to production project
npm run firebase:prod
# or
firebase use production

# Check current project
firebase use
```

## 🔧 Configuration Details

### Environment-Aware Firebase Configuration
The app automatically loads the correct Firebase configuration based on the Vite mode:

- **Development mode** (`npm run dev`): Uses `somayya-academy-dev` project
- **Production mode** (`npm run build:prod`): Uses `somayya-academy` project

### Firebase Configuration Structure
```javascript
// environment/development.js
firebase: {
  apiKey: "...",
  authDomain: "somayya-academy-dev.firebaseapp.com",
  projectId: "somayya-academy-dev",
  // ... other config
}

// environment/production.js
firebase: {
  apiKey: "...",
  authDomain: "somayya-academy.firebaseapp.com",
  projectId: "somayya-academy",
  // ... other config
}
```

## 🧪 Testing

The test setup automatically mocks Firebase configurations for both environments:

```bash
# Run tests
npm test
npm run test:run

# Run tests with coverage
npm run test:coverage
```

## 📁 File Structure

```
├── .firebaserc                 # Firebase project aliases
├── firebase.json              # Firebase hosting configuration
├── environment/
│   ├── development.js         # Dev environment config (includes Firebase)
│   └── production.js          # Prod environment config (includes Firebase)
├── src/
│   ├── config/
│   │   ├── environment.js     # Environment loader
│   │   └── firebase.js        # Unified Firebase config
│   ├── firebase.js            # Legacy Firebase config (deprecated)
│   └── firebaseDev.js         # Legacy Firebase dev config (deprecated)
```

## 🔄 Migration Notes

### Legacy Files (Deprecated)
- `src/firebase.js` - Old hardcoded production Firebase config
- `src/firebaseDev.js` - Old hardcoded development Firebase config

These files are kept for backward compatibility but should not be used in new code.

### New Import Pattern
```javascript
// ✅ Use this (new unified config)
import { auth, googleProvider, microsoftProvider, facebookProvider } from './config/firebase';

// ❌ Don't use this (legacy)
import { auth, googleProvider, microsoftProvider, facebookProvider } from './firebase';
```

## 🚨 Important Notes

1. **Environment Variables**: Firebase configurations are stored in JavaScript files (not .env files) as per project preferences
2. **Security**: API keys in Firebase client configurations are safe to expose publicly
3. **Authentication**: OAuth providers are configured for both environments
4. **Deployment**: Always test in development environment before deploying to production
5. **Project Access**: Ensure your Firebase account has access to both projects before deploying

## 🔍 Troubleshooting

### Common Issues

1. **"Project not found" error**
   - Check if you have access to the Firebase project
   - Verify project ID in environment configuration
   - Run `firebase projects:list` to see available projects

2. **Authentication not working**
   - Verify OAuth provider configurations in Firebase Console
   - Check authorized domains in Firebase Authentication settings
   - Ensure correct Firebase project is being used

3. **Build fails**
   - Check if all environment files are properly configured
   - Verify Firebase configuration syntax
   - Run `npm run lint` to check for syntax errors

### Getting Help
- Check Firebase Console for project settings
- Review browser console for Firebase-related errors
- Verify network connectivity to Firebase services
