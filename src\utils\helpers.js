import { ERROR_MESSAGES } from "./constants.js";

export const safeJsonParse = (jsonString, defaultValue = null) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn("Failed to parse JSON:", error);
    return defaultValue;
  }
};

export const safeJsonStringify = (obj, defaultValue = "{}") => {
  try {
    return JSON.stringify(obj);
  } catch (error) {
    console.warn("Failed to stringify object:", error);
    return defaultValue;
  }
};

export const generateId = (prefix = "") => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 11);
  return prefix ? `${prefix}_${timestamp}_${random}` : `${timestamp}_${random}`;
};

/**
 * Debounce function
 * @param {Function} func - Function to debounce
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func.apply(null, args), delay);
  };
};

/**
 * Throttle function
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, delay) => {
  let lastCall = 0;
  return (...args) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      return func.apply(null, args);
    }
  };
};

/**
 * Format time in MM:SS format
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
export const formatTime = (seconds) => {
  if (isNaN(seconds) || seconds < 0) return "0:00";

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
};

/**
 * Clamp value between min and max
 * @param {number} value - Value to clamp
 * @param {number} min - Minimum value
 * @param {number} max - Maximum value
 * @returns {number} Clamped value
 */
export const clamp = (value, min, max) => {
  return Math.min(Math.max(value, min), max);
};

/**
 * Check if value is empty (null, undefined, empty string, empty array, empty object)
 * @param {*} value - Value to check
 * @returns {boolean} True if empty
 */
export const isEmpty = (value) => {
  if (value == null) return true;
  if (typeof value === "string") return value.trim() === "";
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === "object") return Object.keys(value).length === 0;
  return false;
};

/**
 * Deep clone object
 * @param {*} obj - Object to clone
 * @returns {*} Cloned object
 */
export const deepClone = (obj) => {
  if (obj === null || typeof obj !== "object") return obj;
  if (obj instanceof Date) return new Date(obj.getTime());
  if (obj instanceof Array) return obj.map((item) => deepClone(item));
  if (typeof obj === "object") {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * Strip HTML tags from string
 * @param {string} html - HTML string
 * @returns {string} Plain text
 */
export const stripHtmlTags = (html) => {
  if (!html) return "";
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;
  return tempDiv.textContent || tempDiv.innerText || "";
};

/**
 * Get error message from error object
 * @param {Error|string} error - Error object or string
 * @returns {string} Error message
 */
export const getErrorMessage = (error) => {
  if (typeof error === "string") return error;
  if (error?.name === "NotAllowedError")
    return ERROR_MESSAGES.AUDIO_PLAY_FAILED;
  if (error?.name === "NotSupportedError")
    return ERROR_MESSAGES.AUDIO_NOT_SUPPORTED;
  if (error?.message) return error.message;
  return ERROR_MESSAGES.GENERIC_ERROR;
};

/**
 * Check if device is mobile
 * @returns {boolean} True if mobile device
 */
export const isMobile = () => {
  return window.innerWidth <= 768;
};

/**
 * Check if device supports touch
 * @returns {boolean} True if touch is supported
 */
export const isTouchDevice = () => {
  return "ontouchstart" in window || navigator.maxTouchPoints > 0;
};

/**
 * Validate email address
 * @param {string} email - Email address to validate
 * @returns {boolean} True if valid email address
 */
export const validateEmail = (email) => {
  if (!email || typeof email !== "string") return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};



/**
 * Validate audio URL
 * @param {string} url - Audio URL to validate
 * @returns {boolean} True if valid audio URL
 */
export const isValidAudioUrl = (url) => {
  if (!url || typeof url !== "string") return false;
  const audioExtensions = [".mp3", ".wav", ".ogg", ".m4a", ".aac"];
  return audioExtensions.some((ext) => url.toLowerCase().includes(ext));
};

/**
 * Capitalize first letter of string
 * @param {string} str - String to capitalize
 * @returns {string} Capitalized string
 */
export const capitalizeFirstLetter = (str) => {
  if (!str || typeof str !== "string") return "";
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Truncate text to specified length
 * @param {string} text - Text to truncate
 * @param {number} length - Maximum length
 * @param {string} suffix - Suffix to add (default: "...")
 * @returns {string} Truncated text
 */
export const truncateText = (text, length, suffix = "...") => {
  if (!text || typeof text !== "string") return "";
  if (length <= 0) return suffix;
  if (text.length <= length) return text;
  return text.substring(0, length - suffix.length) + suffix;
};

/**
 * Check if object is empty
 * @param {object} obj - Object to check
 * @returns {boolean} True if empty
 */
export const isEmptyObject = (obj) => {
  if (!obj || typeof obj !== "object" || Array.isArray(obj)) return true;
  return Object.keys(obj).length === 0;
};

/**
 * Convert array to object using key function
 * @param {Array} array - Array to convert
 * @param {string|Function} key - Key property or function
 * @returns {object} Converted object
 */
export const arrayToObject = (array, key) => {
  if (!Array.isArray(array)) return {};
  return array.reduce((obj, item) => {
    const keyValue = typeof key === "function" ? key(item) : item[key];
    obj[keyValue] = item;
    return obj;
  }, {});
};

/**
 * Convert object to array of key-value pairs
 * @param {object} obj - Object to convert
 * @returns {Array} Array of key-value pairs
 */
export const objectToArray = (obj) => {
  if (!obj || typeof obj !== "object") return [];
  return Object.entries(obj).map(([key, value]) => ({ key, value }));
};

/**
 * Remove empty values from object
 * @param {object} obj - Object to clean
 * @returns {object} Cleaned object
 */
export const removeEmptyValues = (obj) => {
  if (!obj || typeof obj !== "object") return {};
  const cleaned = {};
  Object.entries(obj).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== "") {
      cleaned[key] = value;
    }
  });
  return cleaned;
};

/**
 * Sort array by property
 * @param {Array} array - Array to sort
 * @param {string} property - Property to sort by
 * @param {string} order - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted array
 */
export const sortByProperty = (array, property, order = "asc") => {
  if (!Array.isArray(array)) return [];
  return [...array].sort((a, b) => {
    const aVal = a[property];
    const bVal = b[property];
    if (order === "desc") {
      return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
    }
    return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
  });
};

/**
 * Group array by property or function
 * @param {Array} array - Array to group
 * @param {string|Function} key - Key property or function
 * @returns {object} Grouped object
 */
export const groupBy = (array, key) => {
  if (!Array.isArray(array)) return {};
  return array.reduce((groups, item) => {
    const groupKey = typeof key === "function" ? key(item) : item[key];
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(item);
    return groups;
  }, {});
};

/**
 * Get unique values from array
 * @param {Array} array - Array to filter
 * @returns {Array} Array with unique values
 */
export const unique = (array) => {
  if (!Array.isArray(array)) return [];
  return [...new Set(array)];
};

/**
 * Extract provider name from Firebase providerId
 * @param {string} providerId - Firebase provider ID (e.g., "google.com", "microsoft.com")
 * @returns {string} Provider name (e.g., "google", "microsoft")
 */
export const extractProviderName = (providerId) => {
  if (!providerId || typeof providerId !== 'string') return '';
  return providerId.replace('.com', '');
};

/**
 * Extract social login data from Firebase user object
 * @param {Object} firebaseUser - Firebase user object
 * @returns {Object} Extracted social login data
 */
export const extractSocialLoginData = (firebaseUser) => {
  if (!firebaseUser || !firebaseUser.providerData || !firebaseUser.providerData[0]) {
    throw new Error('Invalid Firebase user object or missing provider data');
  }

  const providerData = firebaseUser.providerData[0];

  return {
    providerId: providerData.providerId,
    providerName: extractProviderName(providerData.providerId),
    socialUserId: providerData.uid,
    email: providerData.email || firebaseUser.email
  };
};

/**
 * Get client device information
 * @returns {string} Device type
 */
export const getDeviceType = () => {
  if (isMobile()) return 'mobile';
  if (isTouchDevice()) return 'tablet';
  return 'web';
};

/**
 * Get client IP address (placeholder - requires backend integration)
 * @returns {Promise<string|null>} Client IP address
 */
export const getClientIP = async () => {
  try {
    // This is a placeholder. In a real implementation, you might:
    // 1. Use a service like ipapi.co or ipinfo.io
    // 2. Get it from your backend
    // 3. Use a CDN header if available
    const response = await fetch('https://api.ipify.org?format=json');
    const data = await response.json();
    return data.ip;
  } catch (error) {
    console.warn('Failed to get client IP:', error);
    return null;
  }
};

/**
 * Get client location (placeholder - requires geolocation API or IP-based service)
 * @returns {Promise<string|null>} Client location
 */
export const getClientLocation = async () => {
  try {
    // This is a placeholder. In a real implementation, you might:
    // 1. Use navigator.geolocation API
    // 2. Use an IP-based geolocation service
    // 3. Get it from user preferences
    return 'Mumbai'; // Hardcoded as per requirements
  } catch (error) {
    console.warn('Failed to get client location:', error);
    return null;
  }
};

/**
 * Split array into chunks
 * @param {Array} array - Array to chunk
 * @param {number} size - Chunk size
 * @returns {Array} Array of chunks
 */
export const chunk = (array, size) => {
  if (!Array.isArray(array) || size <= 0) return [];
  const chunks = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

/**
 * Flatten nested arrays
 * @param {Array} array - Array to flatten
 * @returns {Array} Flattened array
 */
export const flatten = (array) => {
  if (!Array.isArray(array)) return [];
  return array.reduce((flat, item) => {
    return flat.concat(Array.isArray(item) ? flatten(item) : item);
  }, []);
};

/**
 * Find intersection of two arrays
 * @param {Array} array1 - First array
 * @param {Array} array2 - Second array
 * @returns {Array} Intersection array
 */
export const intersection = (array1, array2) => {
  if (!Array.isArray(array1) || !Array.isArray(array2)) return [];
  return array1.filter(item => array2.includes(item));
};

/**
 * Find difference between two arrays
 * @param {Array} array1 - First array
 * @param {Array} array2 - Second array
 * @returns {Array} Difference array
 */
export const difference = (array1, array2) => {
  if (!Array.isArray(array1) || !Array.isArray(array2)) return [];
  return array1.filter(item => !array2.includes(item));
};

/**
 * Find union of two arrays
 * @param {Array} array1 - First array
 * @param {Array} array2 - Second array
 * @returns {Array} Union array
 */
export const union = (array1, array2) => {
  if (!Array.isArray(array1)) array1 = [];
  if (!Array.isArray(array2)) array2 = [];
  return unique([...array1, ...array2]);
};
