import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useBookmarkStore } from '../bookmarkStore.js';
import { mockBookmark, mockAnnotation } from '../../test/utils.jsx';

describe('bookmarkStore', () => {
  beforeEach(() => {
    // Reset store state and localStorage before each test
    useBookmarkStore.setState({
      bookmarks: [],
      annotations: [],
    });
    localStorage.clear();
    vi.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = useBookmarkStore.getState();
      expect(state.bookmarks).toEqual([]);
      expect(state.annotations).toEqual([]);
      expect(typeof state.addBookmark).toBe('function');
      expect(typeof state.removeBookmark).toBe('function');
      expect(typeof state.addAnnotation).toBe('function');
      expect(typeof state.removeAnnotation).toBe('function');
    });
  });

  describe('bookmarks', () => {
    it('should add a bookmark', () => {
      const { addBookmark } = useBookmarkStore.getState();
      const bookmarkData = {
        topicId: 'topic-1',
        pageId: 'page-1',
        title: 'Test Bookmark',
        pageNumber: 1,
      };

      const result = addBookmark(bookmarkData);

      expect(result).toMatchObject(bookmarkData);
      expect(result.id).toBeDefined();
      expect(result.createdAt).toBeDefined();

      const state = useBookmarkStore.getState();
      expect(state.bookmarks).toHaveLength(1);
      expect(state.bookmarks[0]).toEqual(result);
    });

    it('should generate unique IDs for bookmarks', () => {
      const { addBookmark } = useBookmarkStore.getState();

      const bookmark1 = addBookmark({ title: 'Bookmark 1' });
      const bookmark2 = addBookmark({ title: 'Bookmark 2' });

      expect(bookmark1.id).not.toBe(bookmark2.id);
      expect(bookmark1.id).toMatch(/^bookmark-/);
      expect(bookmark2.id).toMatch(/^bookmark-/);
    });

    it('should remove a bookmark by ID', () => {
      const { addBookmark, removeBookmark } = useBookmarkStore.getState();

      const bookmark1 = addBookmark({ title: 'Bookmark 1' });
      const bookmark2 = addBookmark({ title: 'Bookmark 2' });

      let state = useBookmarkStore.getState();
      expect(state.bookmarks).toHaveLength(2);

      removeBookmark(bookmark1.id);

      state = useBookmarkStore.getState();
      expect(state.bookmarks).toHaveLength(1);
      expect(state.bookmarks[0].id).toBe(bookmark2.id);
    });

    it('should not throw error when removing non-existent bookmark', () => {
      const { removeBookmark } = useBookmarkStore.getState();

      expect(() => removeBookmark('non-existent-id')).not.toThrow();

      const state = useBookmarkStore.getState();
      expect(state.bookmarks).toHaveLength(0);
    });

    it('should get bookmarks by topic ID', () => {
      const { addBookmark, getBookmarks } = useBookmarkStore.getState();

      addBookmark({ topicId: 'topic-1', title: 'Bookmark 1' });
      addBookmark({ topicId: 'topic-2', title: 'Bookmark 2' });
      addBookmark({ topicId: 'topic-1', title: 'Bookmark 3' });

      const topic1Bookmarks = getBookmarks('topic-1');
      const topic2Bookmarks = getBookmarks('topic-2');

      expect(topic1Bookmarks).toHaveLength(2);
      expect(topic2Bookmarks).toHaveLength(1);
      expect(topic1Bookmarks[0].title).toBe('Bookmark 1');
      expect(topic1Bookmarks[1].title).toBe('Bookmark 3');
    });

    it('should get bookmarks by page ID', () => {
      const { addBookmark, getBookmarks } = useBookmarkStore.getState();

      addBookmark({ pageId: 'page-1', title: 'Bookmark 1' });
      addBookmark({ pageId: 'page-2', title: 'Bookmark 2' });
      addBookmark({ pageId: 'page-1', title: 'Bookmark 3' });

      const page1Bookmarks = getBookmarks(null, 'page-1');
      const page2Bookmarks = getBookmarks(null, 'page-2');

      expect(page1Bookmarks).toHaveLength(2);
      expect(page2Bookmarks).toHaveLength(1);
    });
  });

  describe('annotations', () => {
    it('should add an annotation', () => {
      const { addAnnotation } = useBookmarkStore.getState();
      const annotationData = {
        topicId: 'topic-1',
        pageId: 'page-1',
        title: 'Test Annotation',
        content: 'Test annotation content',
        pageNumber: 1,
      };

      const result = addAnnotation(annotationData);

      expect(result).toMatchObject(annotationData);
      expect(result.id).toBeDefined();
      expect(result.createdAt).toBeDefined();

      const state = useBookmarkStore.getState();
      expect(state.annotations).toHaveLength(1);
      expect(state.annotations[0]).toEqual(result);
    });

    it('should generate unique IDs for annotations', () => {
      const { addAnnotation } = useBookmarkStore.getState();

      const annotation1 = addAnnotation({ title: 'Annotation 1' });
      const annotation2 = addAnnotation({ title: 'Annotation 2' });

      expect(annotation1.id).not.toBe(annotation2.id);
      expect(annotation1.id).toMatch(/^annotation-/);
      expect(annotation2.id).toMatch(/^annotation-/);
    });

    it('should remove an annotation by ID', () => {
      const { addAnnotation, removeAnnotation } = useBookmarkStore.getState();

      const annotation1 = addAnnotation({ title: 'Annotation 1' });
      const annotation2 = addAnnotation({ title: 'Annotation 2' });

      let state = useBookmarkStore.getState();
      expect(state.annotations).toHaveLength(2);

      removeAnnotation(annotation1.id);

      state = useBookmarkStore.getState();
      expect(state.annotations).toHaveLength(1);
      expect(state.annotations[0].id).toBe(annotation2.id);
    });

    it('should update an annotation', () => {
      const { addAnnotation, updateAnnotation } = useBookmarkStore.getState();

      const annotation = addAnnotation({
        title: 'Original Title',
        content: 'Original Content',
      });

      const updates = {
        title: 'Updated Title',
        content: 'Updated Content',
      };

      updateAnnotation(annotation.id, updates);

      const state = useBookmarkStore.getState();
      const updatedAnnotation = state.annotations[0];

      expect(updatedAnnotation.title).toBe('Updated Title');
      expect(updatedAnnotation.content).toBe('Updated Content');
      expect(updatedAnnotation.id).toBe(annotation.id);
      expect(updatedAnnotation.createdAt).toBe(annotation.createdAt);
    });

    it('should get annotations by topic ID', () => {
      const { addAnnotation, getAnnotations } = useBookmarkStore.getState();

      addAnnotation({ topicId: 'topic-1', title: 'Annotation 1' });
      addAnnotation({ topicId: 'topic-2', title: 'Annotation 2' });
      addAnnotation({ topicId: 'topic-1', title: 'Annotation 3' });

      const topic1Annotations = getAnnotations('topic-1');
      const topic2Annotations = getAnnotations('topic-2');

      expect(topic1Annotations).toHaveLength(2);
      expect(topic2Annotations).toHaveLength(1);
    });
  });

  describe('localStorage persistence', () => {
    it('should save to localStorage when bookmark is added', () => {
      const { addBookmark } = useBookmarkStore.getState();

      addBookmark({ title: 'Test Bookmark' });

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'bookmark-annotation-storage',
        expect.stringContaining('Test Bookmark')
      );
    });

    it('should save to localStorage when annotation is added', () => {
      const { addAnnotation } = useBookmarkStore.getState();

      addAnnotation({ title: 'Test Annotation' });

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'bookmark-annotation-storage',
        expect.stringContaining('Test Annotation')
      );
    });

    it('should handle localStorage errors gracefully', () => {
      localStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      const { addBookmark } = useBookmarkStore.getState();

      // Should not throw error
      expect(() => addBookmark({ title: 'Test' })).not.toThrow();

      // State should still be updated
      const state = useBookmarkStore.getState();
      expect(state.bookmarks).toHaveLength(1);
    });
  });
});
