import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import PrivateRoute from '../PrivateRoute.jsx';
import { useAuthStore } from '../../stores/authStore.js';

// Mock the auth store
vi.mock('../../stores/authStore.js', () => ({
  useAuthStore: vi.fn(),
}));

// Mock Navigate component
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    Navigate: vi.fn(({ to, replace }) => (
      <div data-testid="navigate" data-to={to} data-replace={replace}>
        Navigate to {to}
      </div>
    )),
  };
});

const TestComponent = () => <div data-testid="protected-content">Protected Content</div>;

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('PrivateRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render children when user is authenticated', () => {
    useAuthStore.mockReturnValue(true); // isAuthenticated = true
    
    renderWithRouter(
      <PrivateRoute>
        <TestComponent />
      </PrivateRoute>
    );
    
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should redirect to login when user is not authenticated', () => {
    useAuthStore.mockReturnValue(false); // isAuthenticated = false
    
    renderWithRouter(
      <PrivateRoute>
        <TestComponent />
      </PrivateRoute>
    );
    
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-to', '/login');
    expect(screen.getByTestId('navigate')).toHaveAttribute('data-replace', 'true');
  });

  it('should handle multiple children when authenticated', () => {
    useAuthStore.mockReturnValue(true);
    
    renderWithRouter(
      <PrivateRoute>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </PrivateRoute>
    );
    
    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
  });

  it('should not render children when not authenticated', () => {
    useAuthStore.mockReturnValue(false);
    
    renderWithRouter(
      <PrivateRoute>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
      </PrivateRoute>
    );
    
    expect(screen.queryByTestId('child-1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('child-2')).not.toBeInTheDocument();
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
  });

  it('should handle null children when authenticated', () => {
    useAuthStore.mockReturnValue(true);
    
    renderWithRouter(
      <PrivateRoute>
        {null}
      </PrivateRoute>
    );
    
    // Should not throw error and should not redirect
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should handle undefined children when authenticated', () => {
    useAuthStore.mockReturnValue(true);
    
    renderWithRouter(
      <PrivateRoute>
        {undefined}
      </PrivateRoute>
    );
    
    // Should not throw error and should not redirect
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
  });

  it('should re-render when authentication state changes', () => {
    const mockUseAuthStore = vi.fn();
    useAuthStore.mockImplementation(mockUseAuthStore);
    
    // Start unauthenticated
    mockUseAuthStore.mockReturnValue(false);
    
    const { rerender } = renderWithRouter(
      <PrivateRoute>
        <TestComponent />
      </PrivateRoute>
    );
    
    expect(screen.getByTestId('navigate')).toBeInTheDocument();
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    
    // Change to authenticated
    mockUseAuthStore.mockReturnValue(true);
    
    rerender(
      <BrowserRouter>
        <PrivateRoute>
          <TestComponent />
        </PrivateRoute>
      </BrowserRouter>
    );
    
    expect(screen.queryByTestId('navigate')).not.toBeInTheDocument();
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });
});
