# Testing Guide for Somayya Academy

This document provides comprehensive information about the testing setup, strategies, and best practices for the Somayya Academy web application.

## 🧪 Testing Framework & Tools

### Core Testing Stack
- **Vitest** - Fast unit test runner built for Vite
- **@testing-library/react** - Simple and complete testing utilities for React components
- **@testing-library/jest-dom** - Custom Jest matchers for DOM elements
- **jsdom** - DOM implementation for Node.js (test environment)

### Coverage & Reporting
- **v8** - Built-in coverage provider
- **HTML, JSON, Text** - Multiple coverage report formats

## 📁 Test Structure

```
src/
├── test/
│   ├── setup.js           # Global test setup and mocks
│   └── utils.js           # Test utilities and helpers
├── components/
│   ├── ui/
│   │   └── __tests__/     # UI component tests
│   └── common/
│       └── __tests__/     # Common component tests
├── hooks/
│   └── __tests__/         # Custom hooks tests
├── stores/
│   └── __tests__/         # Zustand store tests
├── utils/
│   └── __tests__/         # Utility function tests
└── routes/
    └── __tests__/         # Route guard tests
```

## 🚀 Running Tests

### Quick Commands
```bash
# Run all tests once
npm test
npm run test:run

# Run tests in watch mode (recommended for development)
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run tests with UI interface
npm run test:ui

# Run tests with coverage and UI
npm run test:coverage:ui
```

### Advanced Test Runner
```bash
# Using the custom test runner script
node scripts/test-runner.js all          # Run all tests
node scripts/test-runner.js coverage     # Run with coverage
node scripts/test-runner.js watch        # Watch mode
node scripts/test-runner.js ui           # UI mode
node scripts/test-runner.js pattern stores  # Run specific tests
```

## 📊 Test Coverage

### Current Coverage Areas

#### ✅ Fully Tested Components
- **Utility Functions** (`src/utils/helpers.js`)
  - JSON parsing/stringifying
  - ID generation
  - Validation functions
  - Error handling utilities

- **Authentication Store** (`src/stores/authStore.js`)
  - Login/logout functionality
  - Third-party authentication
  - State persistence

- **Custom Hooks** (`src/hooks/`)
  - Error handling hooks
  - Local storage hooks
  - Debounce utilities

- **UI Components** (`src/components/ui/`)
  - LoadingSpinner variants
  - Toast notifications
  - Error boundaries

- **Route Guards** (`src/routes/`)
  - Private route protection
  - Public route redirection

#### 🔄 Partially Tested
- **Bookmark Store** - Core functionality tested
- **Color Store** - Basic state management tested

#### ⏳ Planned for Testing
- **Audio Store** - Complex audio state management
- **Highlight Store** - PDF highlighting functionality
- **Page Components** - Login, Reading pages
- **Layout Components** - Header, MainContent, ToolsPanel
- **Feature Components** - PDFHighlighter, BookmarkPanel

### Coverage Goals
- **Utilities**: 95%+ coverage
- **Stores**: 90%+ coverage
- **Hooks**: 90%+ coverage
- **Components**: 80%+ coverage
- **Integration**: 70%+ coverage

## 🧩 Test Categories

### 1. Unit Tests
Test individual functions, components, and modules in isolation.

**Examples:**
- Utility function validation
- Component rendering with props
- Store state mutations
- Hook behavior

### 2. Integration Tests
Test how different parts work together.

**Examples:**
- Component + Store interactions
- Route navigation flows
- Authentication workflows

### 3. Component Tests
Test React components with various props and states.

**Examples:**
- Rendering with different props
- User interaction handling
- Error state handling
- Accessibility compliance

## 🛠️ Writing Tests

### Test File Naming
- Unit tests: `ComponentName.test.jsx` or `functionName.test.js`
- Place in `__tests__` folder within the same directory as the source

### Basic Test Structure
```javascript
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import ComponentName from '../ComponentName.jsx';

describe('ComponentName', () => {
  beforeEach(() => {
    // Setup before each test
  });

  it('should render correctly', () => {
    render(<ComponentName />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('should handle user interactions', () => {
    const mockFn = vi.fn();
    render(<ComponentName onClick={mockFn} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockFn).toHaveBeenCalledOnce();
  });
});
```

### Testing Best Practices

#### ✅ Do
- Test behavior, not implementation
- Use descriptive test names
- Test edge cases and error conditions
- Mock external dependencies
- Use `screen` queries from testing-library
- Test accessibility attributes
- Clean up after tests

#### ❌ Don't
- Test internal component state directly
- Test third-party library functionality
- Write tests that depend on other tests
- Use implementation details in assertions
- Ignore async operations
- Skip error scenarios

### Common Testing Patterns

#### Testing Components with Props
```javascript
it('should render with custom props', () => {
  render(<Button variant="primary" size="large">Click me</Button>);
  
  const button = screen.getByRole('button');
  expect(button).toHaveClass('button--primary', 'button--large');
  expect(button).toHaveTextContent('Click me');
});
```

#### Testing User Interactions
```javascript
it('should call onClick when clicked', () => {
  const handleClick = vi.fn();
  render(<Button onClick={handleClick}>Click me</Button>);
  
  fireEvent.click(screen.getByRole('button'));
  expect(handleClick).toHaveBeenCalledOnce();
});
```

#### Testing Async Operations
```javascript
it('should handle async operations', async () => {
  const mockFetch = vi.fn().mockResolvedValue({ data: 'test' });
  render(<AsyncComponent fetch={mockFetch} />);
  
  await waitFor(() => {
    expect(screen.getByText('test')).toBeInTheDocument();
  });
});
```

#### Testing Error States
```javascript
it('should display error message on failure', () => {
  render(<Component hasError={true} errorMessage="Something went wrong" />);
  
  expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  expect(screen.getByRole('alert')).toBeInTheDocument();
});
```

## 🔧 Test Configuration

### Vitest Configuration (`vite.config.js`)
```javascript
export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js'],
    css: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.js',
        'dist/',
        'coverage/',
        '**/*.test.{js,jsx}',
        '**/*.spec.{js,jsx}'
      ]
    }
  }
});
```

### Global Setup (`src/test/setup.js`)
- Imports `@testing-library/jest-dom` matchers
- Mocks Firebase authentication
- Mocks localStorage/sessionStorage
- Mocks browser APIs (matchMedia, IntersectionObserver)
- Sets up global cleanup

## 📈 Continuous Integration

### Pre-commit Hooks
```bash
# Run tests before committing
npm run test:run
```

### CI Pipeline
```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: npm run test:run

- name: Generate Coverage
  run: npm run test:coverage

- name: Upload Coverage
  uses: codecov/codecov-action@v3
```

## 🐛 Debugging Tests

### Common Issues
1. **Tests timing out**: Increase timeout or fix async operations
2. **DOM not updating**: Use `waitFor` for async updates
3. **Mocks not working**: Check mock setup and imports
4. **Coverage not accurate**: Verify file paths and exclusions

### Debugging Commands
```bash
# Run specific test file
npx vitest run src/components/ui/__tests__/Button.test.jsx

# Run tests matching pattern
npx vitest run --grep "should render"

# Run with verbose output
npx vitest run --reporter=verbose
```

## 📚 Resources

- [Vitest Documentation](https://vitest.dev/)
- [Testing Library Docs](https://testing-library.com/)
- [Jest DOM Matchers](https://github.com/testing-library/jest-dom)
- [React Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

---

## 🎯 Next Steps

1. **Expand Component Tests**: Add tests for remaining UI components
2. **Integration Testing**: Test component interactions with stores
3. **E2E Testing**: Consider adding Playwright or Cypress
4. **Performance Testing**: Add performance benchmarks
5. **Visual Regression**: Consider adding visual testing tools

Happy Testing! 🧪✨
