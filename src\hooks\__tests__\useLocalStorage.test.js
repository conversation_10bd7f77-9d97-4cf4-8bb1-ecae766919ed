import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useLocalStorage, useSessionStorage } from '../useLocalStorage.js';

describe('useLocalStorage', () => {
  beforeEach(() => {
    localStorage.clear();
    vi.clearAllMocks();
  });

  it('should initialize with initial value when localStorage is empty', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));

    expect(result.current[0]).toBe('initial-value');
  });

  it('should initialize with value from localStorage when available', () => {
    localStorage.getItem.mockReturnValue(JSON.stringify('stored-value'));

    const { result } = renderHook(() => useLocalStorage('test-key', 'initial-value'));

    expect(result.current[0]).toBe('stored-value');
  });

  it('should update localStorage when value changes', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

    act(() => {
      result.current[1]('updated-value');
    });

    expect(result.current[0]).toBe('updated-value');
    expect(localStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify('updated-value'));
  });

  it('should handle function updates', () => {
    // Clear any previous localStorage mock calls
    localStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useLocalStorage('test-key', 10));

    act(() => {
      result.current[1](prev => prev + 5);
    });

    expect(result.current[0]).toBe(15);
    expect(localStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(15));
  });

  it('should handle complex objects', () => {
    const initialObject = { name: 'test', count: 0 };
    const { result } = renderHook(() => useLocalStorage('test-key', initialObject));

    const updatedObject = { name: 'updated', count: 5 };

    act(() => {
      result.current[1](updatedObject);
    });

    expect(result.current[0]).toEqual(updatedObject);
    expect(localStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(updatedObject));
  });

  it('should remove value from localStorage', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

    act(() => {
      result.current[2](); // removeValue
    });

    expect(result.current[0]).toBe('initial'); // Should revert to initial value
    expect(localStorage.removeItem).toHaveBeenCalledWith('test-key');
  });

  it('should handle localStorage errors gracefully', () => {
    // Mock localStorage.setItem to throw an error
    localStorage.setItem.mockImplementation(() => {
      throw new Error('Storage quota exceeded');
    });

    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

    // Should not throw error when setting value
    act(() => {
      result.current[1]('new-value');
    });

    // Value should still be updated in state even if localStorage fails
    expect(result.current[0]).toBe('new-value');
  });

  it('should handle invalid JSON in localStorage', () => {
    localStorage.getItem.mockReturnValue('invalid-json{');

    const { result } = renderHook(() => useLocalStorage('test-key', 'fallback'));

    expect(result.current[0]).toBe('fallback');
  });

  it('should handle null values', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', null));

    act(() => {
      result.current[1]('not-null');
    });

    expect(result.current[0]).toBe('not-null');

    act(() => {
      result.current[1](null);
    });

    expect(result.current[0]).toBeNull();
  });

  it('should handle array values', () => {
    const initialArray = [1, 2, 3];
    const { result } = renderHook(() => useLocalStorage('test-key', initialArray));

    const updatedArray = [4, 5, 6];

    act(() => {
      result.current[1](updatedArray);
    });

    expect(result.current[0]).toEqual(updatedArray);
  });
});

describe('useSessionStorage', () => {
  beforeEach(() => {
    sessionStorage.clear();
    vi.clearAllMocks();
  });

  it('should initialize with initial value when sessionStorage is empty', () => {
    const { result } = renderHook(() => useSessionStorage('test-key', 'initial-value'));

    expect(result.current[0]).toBe('initial-value');
  });

  it('should initialize with value from sessionStorage when available', () => {
    sessionStorage.getItem.mockReturnValue(JSON.stringify('stored-value'));

    const { result } = renderHook(() => useSessionStorage('test-key', 'initial-value'));

    expect(result.current[0]).toBe('stored-value');
  });

  it('should update sessionStorage when value changes', () => {
    const { result } = renderHook(() => useSessionStorage('test-key', 'initial'));

    act(() => {
      result.current[1]('updated-value');
    });

    expect(result.current[0]).toBe('updated-value');
    expect(sessionStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify('updated-value'));
  });

  it('should remove value from sessionStorage', () => {
    const { result } = renderHook(() => useSessionStorage('test-key', 'initial'));

    act(() => {
      result.current[2](); // removeValue
    });

    expect(result.current[0]).toBe('initial');
    expect(sessionStorage.removeItem).toHaveBeenCalledWith('test-key');
  });

  it('should handle sessionStorage errors gracefully', () => {
    sessionStorage.setItem.mockImplementation(() => {
      throw new Error('Storage quota exceeded');
    });

    const { result } = renderHook(() => useSessionStorage('test-key', 'initial'));

    act(() => {
      result.current[1]('new-value');
    });

    expect(result.current[0]).toBe('new-value');
  });

  it('should handle invalid JSON in sessionStorage', () => {
    sessionStorage.getItem.mockReturnValue('invalid-json{');

    const { result } = renderHook(() => useSessionStorage('test-key', 'fallback'));

    expect(result.current[0]).toBe('fallback');
  });

  it('should handle function updates in sessionStorage', () => {
    sessionStorage.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useSessionStorage('test-key', 10));

    act(() => {
      result.current[1](prev => prev * 2);
    });

    expect(result.current[0]).toBe(20);
    expect(sessionStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(20));
  });

  it('should handle complex objects in sessionStorage', () => {
    const complexObject = {
      user: { name: 'John', preferences: { theme: 'dark' } },
      settings: [1, 2, 3]
    };

    const { result } = renderHook(() => useSessionStorage('test-key', {}));

    act(() => {
      result.current[1](complexObject);
    });

    expect(result.current[0]).toEqual(complexObject);
    expect(sessionStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(complexObject));
  });
});

describe('useLocalStorage - Additional Edge Cases', () => {
  beforeEach(() => {
    localStorage.clear();
    vi.clearAllMocks();
  });

  it('should handle boolean values correctly', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', false));

    act(() => {
      result.current[1](true);
    });

    expect(result.current[0]).toBe(true);
    expect(localStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(true));
  });

  it('should handle number values correctly', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', 0));

    act(() => {
      result.current[1](42.5);
    });

    expect(result.current[0]).toBe(42.5);
    expect(localStorage.setItem).toHaveBeenCalledWith('test-key', JSON.stringify(42.5));
  });

  it('should handle undefined initial value', () => {
    const { result } = renderHook(() => useLocalStorage('test-key', undefined));

    // When initial value is undefined and localStorage is empty, it should return null
    expect(result.current[0]).toBeNull();

    act(() => {
      result.current[1]('defined');
    });

    expect(result.current[0]).toBe('defined');
  });

  it('should persist across hook re-renders', () => {
    localStorage.getItem.mockReturnValue(JSON.stringify('persisted-value'));

    const { result, rerender } = renderHook(() => useLocalStorage('test-key', 'initial'));

    expect(result.current[0]).toBe('persisted-value');

    rerender();

    expect(result.current[0]).toBe('persisted-value');
  });

  it('should handle storage events', () => {
    // Clear localStorage and reset mocks to ensure clean state
    localStorage.removeItem('test-key');
    localStorage.getItem.mockClear();
    localStorage.setItem.mockClear();

    // Reset mock implementations to default behavior
    localStorage.getItem.mockReturnValue(null);
    localStorage.setItem.mockImplementation(() => {});

    const { result } = renderHook(() => useLocalStorage('test-key', 'initial'));

    expect(result.current[0]).toBe('initial');

    // Simulate storage event from another tab using a simpler approach
    // First update localStorage directly
    localStorage.setItem('test-key', JSON.stringify('updated-from-another-tab'));

    // Create a custom event that mimics storage event
    const storageEvent = new Event('storage');
    Object.defineProperty(storageEvent, 'key', {
      value: 'test-key',
      writable: false
    });
    Object.defineProperty(storageEvent, 'newValue', {
      value: JSON.stringify('updated-from-another-tab'),
      writable: false
    });

    act(() => {
      window.dispatchEvent(storageEvent);
    });

    // The hook should update when storage events occur (good behavior!)
    expect(result.current[0]).toBe('updated-from-another-tab');
  });

  it('should handle very large values', () => {
    const largeObject = {
      data: new Array(1000).fill(0).map((_, i) => ({ id: i, value: `item-${i}` }))
    };

    const { result } = renderHook(() => useLocalStorage('test-key', {}));

    act(() => {
      result.current[1](largeObject);
    });

    expect(result.current[0]).toEqual(largeObject);
  });

  it('should handle special characters in keys', () => {
    const specialKey = 'test-key-with-special-chars-!@#$%^&*()';
    const { result } = renderHook(() => useLocalStorage(specialKey, 'initial'));

    act(() => {
      result.current[1]('updated');
    });

    expect(result.current[0]).toBe('updated');
    expect(localStorage.setItem).toHaveBeenCalledWith(specialKey, JSON.stringify('updated'));
  });
});
