import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import OTPVerificationPage from '../OTPVerificationPage.jsx';

// Mock components that might have complex dependencies
vi.mock('../../../components/common/AnimationBox', () => ({
  default: ({ children }) => <div data-testid="animation-box">{children}</div>
}));

vi.mock('../../../components/layout/Header', () => ({
  default: () => <div data-testid="header">Header</div>
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ state: { email: '<EMAIL>' } }),
  };
});

describe('OTPVerificationPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderWithRouter = (component) => {
    return render(
      <MemoryRouter>
        {component}
      </MemoryRouter>
    );
  };

  describe('Rendering', () => {
    it('should render OTP verification form', () => {
      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByTestId('header')).toBeInTheDocument();
      expect(screen.getByTestId('animation-box')).toBeInTheDocument();
      expect(screen.getByText(/verify your email/i)).toBeInTheDocument();
    });

    it('should render OTP input fields', () => {
      renderWithRouter(<OTPVerificationPage />);

      // Should have 6 OTP input fields (they are spinbutton role because type="number")
      const otpInputs = screen.getAllByRole('spinbutton');
      expect(otpInputs.length).toBe(6);
    });

    it('should display user email', () => {
      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByText(/<EMAIL>/i)).toBeInTheDocument();
    });

    it('should render resend code button', () => {
      renderWithRouter(<OTPVerificationPage />);

      expect(screen.getByText(/resend code/i)).toBeInTheDocument();
    });
  });

  describe('Basic functionality', () => {
    it('should handle OTP input changes', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('spinbutton');

      if (otpInputs.length > 0) {
        fireEvent.change(otpInputs[0], { target: { value: '1' } });
        expect(otpInputs[0].value).toBe('1');
      }
    });

    it('should support keyboard navigation', () => {
      renderWithRouter(<OTPVerificationPage />);

      const otpInputs = screen.getAllByRole('spinbutton');

      if (otpInputs.length > 0) {
        otpInputs[0].focus();
        expect(document.activeElement).toBe(otpInputs[0]);
      }
    });
  });

});
